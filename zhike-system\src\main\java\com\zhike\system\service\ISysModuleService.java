package com.zhike.system.service;

import java.util.List;
import java.util.Map;
import org.springframework.web.multipart.MultipartFile;
import com.zhike.system.domain.SysModule;

/**
 * 系统模块 服务层
 */
public interface ISysModuleService {
    /**
     * 查询系统模块数据
     * 
     * @param moduleId 系统模块ID
     * @return 系统模块信息
     */
    public SysModule selectModuleById(Long moduleId);

    /**
     * 查询系统模块列表
     * 
     * @param module 系统模块信息
     * @return 系统模块集合
     */
    public List<SysModule> selectModuleList(SysModule module);

    /**
     * 新增系统模块信息
     * 
     * @param module 系统模块信息
     * @return 结果
     */
    public int insertModule(SysModule module);

    /**
     * 修改系统模块信息
     * 
     * @param module 系统模块信息
     * @return 结果
     */
    public int updateModule(SysModule module);

    /**
     * 删除系统模块信息
     * 
     * @param moduleId 系统模块ID
     * @return 结果
     */
    public int deleteModuleById(Long moduleId);

    /**
     * 批量删除系统模块信息
     * 
     * @param moduleIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteModuleByIds(Long[] moduleIds);

    /**
     * 查询系统模块是否存在子节点
     *
     * @param moduleId 系统模块ID
     * @return 结果
     */
    public int hasChildByModuleId(Long moduleId);

    /**
     * 解析HTML文件中的模块数据
     *
     * @param file HTML文件
     * @return 解析出的模块数据
     */
    public List<Map<String, Object>> parseHtmlFile(MultipartFile file) throws Exception;

    /**
     * 批量导入模块
     *
     * @param moduleData 模块数据列表
     * @param operName   操作人员
     * @return 导入成功的数量
     */
    public int batchImportModules(List<Map<String, Object>> moduleData, String operName) throws Exception;

    /**
     * 批量导入模块（指定父模块）
     *
     * @param moduleData     模块数据列表
     * @param parentModuleId 父模块ID，为null时表示导入为根模块
     * @param operName       操作人员
     * @return 导入成功的数量
     */
    public int batchImportModules(List<Map<String, Object>> moduleData, Long parentModuleId, String operName)
            throws Exception;
}