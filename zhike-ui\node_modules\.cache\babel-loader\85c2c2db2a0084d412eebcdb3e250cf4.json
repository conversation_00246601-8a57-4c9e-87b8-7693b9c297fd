{"remainingRequest": "D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\ruoyi\\Zhike\\zhike-ui\\src\\api\\system\\module.js", "dependencies": [{"path": "D:\\ruoyi\\Zhike\\zhike-ui\\src\\api\\system\\module.js", "mtime": 1754210114686}, {"path": "D:\\ruoyi\\Zhike\\zhike-ui\\babel.config.js", "mtime": 1737337534038}, {"path": "D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737340298796}, {"path": "D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737340300972}, {"path": "D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1737340299331}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listModule", "query", "request", "url", "method", "params", "getModule", "moduleId", "addModule", "data", "updateModule", "delModule", "parseHtmlFile", "file", "formData", "FormData", "append", "headers", "batchImportModules", "moduleData"], "sources": ["D:/ruoyi/Zhike/zhike-ui/src/api/system/module.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询系统模块列表\r\nexport function listModule(query) {\r\n    return request({\r\n        url: '/system/module/list',\r\n        method: 'get',\r\n        params: query\r\n    })\r\n}\r\n\r\n// 查询系统模块详细\r\nexport function getModule(moduleId) {\r\n    return request({\r\n        url: '/system/module/' + moduleId,\r\n        method: 'get'\r\n    })\r\n}\r\n\r\n// 新增系统模块\r\nexport function addModule(data) {\r\n    return request({\r\n        url: '/system/module',\r\n        method: 'post',\r\n        data: data\r\n    })\r\n}\r\n\r\n// 修改系统模块\r\nexport function updateModule(data) {\r\n    return request({\r\n        url: '/system/module',\r\n        method: 'put',\r\n        data: data\r\n    })\r\n}\r\n\r\n// 删除系统模块\r\nexport function delModule(moduleId) {\r\n    return request({\r\n        url: '/system/module/' + moduleId,\r\n        method: 'delete'\r\n    })\r\n}\r\n\r\n// 解析HTML文件中的模块数据\r\nexport function parseHtmlFile(file) {\r\n    const formData = new FormData()\r\n    formData.append('file', file)\r\n    return request({\r\n        url: '/system/module/parse-html',\r\n        method: 'post',\r\n        data: formData,\r\n        headers: {\r\n            'Content-Type': 'multipart/form-data'\r\n        }\r\n    })\r\n}\r\n\r\n// 批量导入模块\r\nexport function batchImportModules(moduleData) {\r\n    return request({\r\n        url: '/system/module/batch-import',\r\n        method: 'post',\r\n        data: moduleData\r\n    })\r\n}"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACXC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACZ,CAAC,CAAC;AACN;;AAEA;AACO,SAASK,SAASA,CAACC,QAAQ,EAAE;EAChC,OAAO,IAAAL,gBAAO,EAAC;IACXC,GAAG,EAAE,iBAAiB,GAAGI,QAAQ;IACjCH,MAAM,EAAE;EACZ,CAAC,CAAC;AACN;;AAEA;AACO,SAASI,SAASA,CAACC,IAAI,EAAE;EAC5B,OAAO,IAAAP,gBAAO,EAAC;IACXC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACV,CAAC,CAAC;AACN;;AAEA;AACO,SAASC,YAAYA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAP,gBAAO,EAAC;IACXC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACV,CAAC,CAAC;AACN;;AAEA;AACO,SAASE,SAASA,CAACJ,QAAQ,EAAE;EAChC,OAAO,IAAAL,gBAAO,EAAC;IACXC,GAAG,EAAE,iBAAiB,GAAGI,QAAQ;IACjCH,MAAM,EAAE;EACZ,CAAC,CAAC;AACN;;AAEA;AACO,SAASQ,aAAaA,CAACC,IAAI,EAAE;EAChC,IAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;EAC7B,OAAO,IAAAX,gBAAO,EAAC;IACXC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEK,QAAQ;IACdG,OAAO,EAAE;MACL,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;AACN;;AAEA;AACO,SAASC,kBAAkBA,CAACC,UAAU,EAAE;EAC3C,OAAO,IAAAjB,gBAAO,EAAC;IACXC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEU;EACV,CAAC,CAAC;AACN", "ignoreList": []}]}