package com.zhike.system.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import com.zhike.common.exception.ServiceException;
import com.zhike.common.utils.StringUtils;
import com.zhike.system.domain.SysModule;
import com.zhike.system.mapper.SysModuleMapper;
import com.zhike.system.service.ISysModuleService;

/**
 * 系统模块 服务层处理
 */
@Service
public class SysModuleServiceImpl implements ISysModuleService {
    @Autowired
    private SysModuleMapper moduleMapper;

    /**
     * 查询系统模块数据
     * 
     * @param moduleId 系统模块ID
     * @return 系统模块信息
     */
    @Override
    public SysModule selectModuleById(Long moduleId) {
        return moduleMapper.selectModuleById(moduleId);
    }

    /**
     * 查询系统模块列表
     * 
     * @param module 系统模块信息
     * @return 系统模块集合
     */
    @Override
    public List<SysModule> selectModuleList(SysModule module) {
        return moduleMapper.selectModuleList(module);
    }

    /**
     * 新增系统模块信息
     * 
     * @param module 系统模块信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertModule(SysModule module) {
        SysModule info = moduleMapper.selectModuleById(module.getParentId());
        // 如果父节点不为"正常"状态,则不允许新增子节点
        if (StringUtils.isNotNull(info) && !("0").equals(info.getStatus())) {
            throw new ServiceException("系统模块停用，不允许新增");
        }
        module.setAncestors(getAncestors(module.getParentId()));
        return moduleMapper.insertModule(module);
    }

    /**
     * 修改系统模块信息
     * 
     * @param module 系统模块信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateModule(SysModule module) {
        SysModule newParentModule = moduleMapper.selectModuleById(module.getParentId());
        SysModule oldModule = selectModuleById(module.getModuleId());
        if (StringUtils.isNotNull(newParentModule) && StringUtils.isNotNull(oldModule)) {
            String newAncestors = newParentModule.getAncestors() + "," + newParentModule.getModuleId();
            String oldAncestors = oldModule.getAncestors();
            module.setAncestors(newAncestors);
            updateModuleChildren(module.getModuleId(), newAncestors, oldAncestors);
        } else if (module.getParentId() == 0 && StringUtils.isNotNull(oldModule)) {
            // 如果是变更为顶级模块
            module.setAncestors("0");
            updateModuleChildren(module.getModuleId(), "0", oldModule.getAncestors());
        }
        return moduleMapper.updateModule(module);
    }

    /**
     * 修改子元素关系
     * 
     * @param moduleId     被修改的系统模块ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateModuleChildren(Long moduleId, String newAncestors, String oldAncestors) {
        SysModule module = new SysModule();
        module.setParentId(moduleId);
        List<SysModule> children = moduleMapper.selectModuleList(module);
        for (SysModule child : children) {
            String ancestors = child.getAncestors();
            if (ancestors != null && ancestors.startsWith(oldAncestors)) {
                String newChildAncestors = newAncestors + ancestors.substring(oldAncestors.length());
                SysModule childModule = new SysModule();
                childModule.setModuleId(child.getModuleId());
                childModule.setAncestors(newChildAncestors);
                moduleMapper.updateModule(childModule);
            }
        }
    }

    /**
     * 删除系统模块管理信息
     * 
     * @param moduleId 系统模块ID
     * @return 结果
     */
    @Override
    public int deleteModuleById(Long moduleId) {
        return moduleMapper.deleteModuleById(moduleId);
    }

    /**
     * 批量删除系统模块信息
     * 
     * @param moduleIds 需要删除的系统模块ID
     * @return 结果
     */
    @Override
    public int deleteModuleByIds(Long[] moduleIds) {
        for (Long moduleId : moduleIds) {
            SysModule module = selectModuleById(moduleId);
            if (hasChildByModuleId(moduleId) > 0) {
                throw new ServiceException(module.getModuleName() + "存在下级模块,不允许删除");
            }
        }
        return moduleMapper.deleteModuleByIds(moduleIds);
    }

    /**
     * 查询系统模块是否存在子节点
     *
     * @param moduleId 系统模块ID
     * @return 结果
     */
    @Override
    public int hasChildByModuleId(Long moduleId) {
        return moduleMapper.hasChildByModuleId(moduleId);
    }

    /**
     * 解析HTML文件中的模块数据
     */
    @Override
    public List<Map<String, Object>> parseHtmlFile(MultipartFile file) throws Exception {
        List<Map<String, Object>> result = new ArrayList<>();
        try {
            String htmlContent = new String(file.getBytes(), "UTF-8");
            Document doc = Jsoup.parse(htmlContent);

            // 1. 解析左侧的分类树，构建完整的层级结构
            Elements leftTreeRoots = doc.select("div[data-v-792b698e] .el-tree > .el-tree-node");
            int orderNum = 1;
            for (Element node : leftTreeRoots) {
                orderNum = parseLeftTreeNode(node, null, result, orderNum);
            }

            // 2. 找到左侧树中展开路径的最深层级节点，作为右侧产品树的父节点
            String productTreeParent = findExpandedLeafNode(leftTreeRoots);

            // 3. 解析右侧的产品树
            Element productTree = doc.selectFirst("div.product-tree[data-v-031cdc77]");
            if (productTree != null) {
                Elements topLevelNodes = productTree.select("> .el-tree-node");
                for (Element node : topLevelNodes) {
                    orderNum = parseProductNode(node, productTreeParent, result, orderNum);
                }
            }

        } catch (IOException e) {
            throw new ServiceException("读取HTML文件失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 解析左侧树节点，处理所有节点构建完整的层级结构
     */
    private int parseLeftTreeNode(Element node, String parentName, List<Map<String, Object>> result, int orderNum) {
        Element content = node.selectFirst("> .el-tree-node__content");
        if (content == null)
            return orderNum;

        Element tooltip = content.selectFirst(".el-tooltip");
        if (tooltip == null)
            return orderNum;

        // 提取节点文本，忽略子元素如数量统计
        String nodeText = tooltip.ownText().trim();
        if (StringUtils.isEmpty(nodeText))
            return orderNum;

        // 创建模块数据
        Map<String, Object> moduleData = new HashMap<>();
        moduleData.put("moduleName", nodeText);
        moduleData.put("moduleCode", convertToPinyin(nodeText));
        moduleData.put("parentName", parentName);
        moduleData.put("orderNum", orderNum++);
        moduleData.put("selected", true);

        // 检查是否有子节点
        Element childrenContainer = node.selectFirst("> .el-tree-node__children");
        boolean isParent = childrenContainer != null && !childrenContainer.children().isEmpty();
        moduleData.put("isParent", isParent);

        result.add(moduleData);

        // 递归处理所有子节点
        if (isParent && childrenContainer != null) {
            Elements children = childrenContainer.select("> .el-tree-node");
            for (Element child : children) {
                orderNum = parseLeftTreeNode(child, nodeText, result, orderNum);
            }
        }

        return orderNum;
    }

    /**
     * 找到左侧树中展开路径的最深层级节点
     */
    private String findExpandedLeafNode(Elements leftTreeRoots) {
        for (Element node : leftTreeRoots) {
            String leafNode = findExpandedLeafNodeRecursive(node);
            if (leafNode != null) {
                return leafNode;
            }
        }
        return null;
    }

    /**
     * 递归查找展开路径的最深层级节点
     */
    private String findExpandedLeafNodeRecursive(Element node) {
        // 只处理展开的节点
        if (!node.hasClass("is-expanded")) {
            return null;
        }

        Element content = node.selectFirst("> .el-tree-node__content");
        if (content == null)
            return null;

        Element tooltip = content.selectFirst(".el-tooltip");
        if (tooltip == null)
            return null;

        String nodeText = tooltip.ownText().trim();
        if (StringUtils.isEmpty(nodeText))
            return null;

        // 检查是否有展开的子节点
        Element childrenContainer = node.selectFirst("> .el-tree-node__children");
        if (childrenContainer != null) {
            Elements expandedChildren = childrenContainer.select("> .el-tree-node.is-expanded");
            if (!expandedChildren.isEmpty()) {
                // 有展开的子节点，继续递归查找
                for (Element child : expandedChildren) {
                    String childLeaf = findExpandedLeafNodeRecursive(child);
                    if (childLeaf != null) {
                        return childLeaf;
                    }
                }
            }
        }

        // 没有展开的子节点，当前节点就是叶子节点
        return nodeText;
    }

    private int parseProductNode(Element node, String parentName, List<Map<String, Object>> result, int orderNum) {
        Element content = node.selectFirst("> .el-tree-node__content");
        if (content == null)
            return orderNum;

        String moduleName = null;

        // Try to find the name in a parent node structure
        Element parentNameSpan = content.selectFirst(".tree-node-parent .flex1 > span:first-of-type");
        if (parentNameSpan != null) {
            moduleName = parentNameSpan.text().trim();
        } else {
            // If not a parent node, try the leaf node structure
            Element leafNameSpan = content.selectFirst(".tree-node-leaf .el-popover__reference");
            if (leafNameSpan != null) {
                moduleName = leafNameSpan.text().trim();
            }
        }

        if (StringUtils.isEmpty(moduleName)) {
            return orderNum;
        }

        Map<String, Object> moduleData = new HashMap<>();
        moduleData.put("moduleName", moduleName);
        moduleData.put("moduleCode", convertToPinyin(moduleName));
        moduleData.put("parentName", parentName);
        moduleData.put("orderNum", orderNum++);
        moduleData.put("selected", true);

        Element childrenContainer = node.selectFirst("> .el-tree-node__children");
        boolean isParent = childrenContainer != null && !childrenContainer.children().isEmpty();
        moduleData.put("isParent", isParent);

        result.add(moduleData);

        if (isParent && childrenContainer != null) {
            Elements children = childrenContainer.select("> .el-tree-node");
            for (Element child : children) {
                orderNum = parseProductNode(child, moduleName, result, orderNum);
            }
        }
        return orderNum;
    }

    /**
     * 批量导入模块
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchImportModules(List<Map<String, Object>> moduleData, String operName) throws Exception {
        return batchImportModules(moduleData, null, operName);
    }

    /**
     * 批量导入模块（指定父模块）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchImportModules(List<Map<String, Object>> moduleData, Long parentModuleId, String operName)
            throws Exception {
        int importCount = 0;
        Map<String, Long> createdModules = new HashMap<>();

        // Pre-populate with existing modules to avoid duplicates and find existing
        // parents
        List<SysModule> allModules = moduleMapper.selectModuleList(new SysModule());
        for (SysModule mod : allModules) {
            createdModules.put(mod.getModuleName(), mod.getModuleId());
        }

        List<Map<String, Object>> pendingModules = new ArrayList<>(moduleData);
        int lastPendingCount = -1;

        // Loop until all modules are processed or no progress can be made (to handle
        // parent-child order)
        while (!pendingModules.isEmpty() && pendingModules.size() != lastPendingCount) {
            lastPendingCount = pendingModules.size();
            List<Map<String, Object>> nextPending = new ArrayList<>();

            for (Map<String, Object> data : pendingModules) {
                String moduleName = (String) data.get("moduleName");
                String parentName = (String) data.get("parentName");

                Long parentId = parentModuleId != null ? parentModuleId : 0L;

                // 如果指定了父模块ID，则忽略数据中的parentName
                if (parentModuleId == null) {
                    // Check if parent needs to be created first
                    if (StringUtils.isNotEmpty(parentName)) {
                        if (createdModules.containsKey(parentName)) {
                            parentId = createdModules.get(parentName);
                        } else {
                            // Parent not created yet, defer this module
                            nextPending.add(data);
                            continue;
                        }
                    }
                }

                // Check if this specific module (with this name and parent) already exists
                SysModule query = new SysModule();
                query.setModuleName(moduleName);
                query.setParentId(parentId);
                List<SysModule> existing = moduleMapper.selectModuleList(query);

                if (existing.isEmpty()) {
                    // Module does not exist, create it
                    SysModule module = new SysModule();
                    module.setModuleName(moduleName);
                    module.setModuleCode((String) data.get("moduleCode"));
                    module.setParentId(parentId);
                    module.setAncestors(getAncestors(parentId));
                    module.setOrderNum((Integer) data.get("orderNum"));

                    Boolean isParent = (Boolean) data.get("isParent");
                    module.setModuleType("2"); // 业务模块
                    module.setModuleProperty((isParent != null && isParent) ? "1" : "3"); // 1:目录, 3:功能

                    module.setStatus("0");
                    module.setCreateBy(operName);

                    moduleMapper.insertModule(module);
                    // Add the newly created module to our map
                    createdModules.put(moduleName, module.getModuleId());
                    importCount++;
                } else {
                    // Module already exists, just ensure it's in our map for its children
                    createdModules.putIfAbsent(moduleName, existing.get(0).getModuleId());
                }
            }
            pendingModules = nextPending;
        }

        if (!pendingModules.isEmpty()) {
            String missingModules = pendingModules.stream()
                    .map(m -> (String) m.get("moduleName") + " (父: " + m.get("parentName") + ")")
                    .collect(java.util.stream.Collectors.joining(", "));
            throw new ServiceException("导入失败，部分模块的父模块不存在: " + missingModules);
        }

        return importCount;
    }

    /**
     * 汉字转拼音首字母
     */
    private String convertToPinyin(String chinese) {
        if (StringUtils.isEmpty(chinese)) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        Pattern pattern = Pattern.compile("[\\u4e00-\\u9fa5]");

        for (char c : chinese.toCharArray()) {
            Matcher matcher = pattern.matcher(String.valueOf(c));
            if (matcher.matches()) {
                // 简单的汉字转拼音首字母映射
                result.append(getFirstLetter(c));
            } else if (Character.isLetter(c)) {
                result.append(Character.toUpperCase(c));
            }
        }

        return result.toString();
    }

    /**
     * 获取汉字的拼音首字母
     */
    private char getFirstLetter(char c) {
        // 简化版本的汉字转拼音首字母
        int code = (int) c;
        if (code >= 0x4e00 && code <= 0x9fa5) {
            if (code >= 0x4e00 && code <= 0x4fff)
                return 'A';
            if (code >= 0x5000 && code <= 0x53ff)
                return 'B';
            if (code >= 0x5400 && code <= 0x57ff)
                return 'C';
            if (code >= 0x5800 && code <= 0x5bff)
                return 'D';
            if (code >= 0x5c00 && code <= 0x5fff)
                return 'E';
            if (code >= 0x6000 && code <= 0x63ff)
                return 'F';
            if (code >= 0x6400 && code <= 0x67ff)
                return 'G';
            if (code >= 0x6800 && code <= 0x6bff)
                return 'H';
            if (code >= 0x6c00 && code <= 0x6fff)
                return 'I';
            if (code >= 0x7000 && code <= 0x73ff)
                return 'J';
            if (code >= 0x7400 && code <= 0x77ff)
                return 'K';
            if (code >= 0x7800 && code <= 0x7bff)
                return 'L';
            if (code >= 0x7c00 && code <= 0x7fff)
                return 'M';
            if (code >= 0x8000 && code <= 0x83ff)
                return 'N';
            if (code >= 0x8400 && code <= 0x87ff)
                return 'O';
            if (code >= 0x8800 && code <= 0x8bff)
                return 'P';
            if (code >= 0x8c00 && code <= 0x8fff)
                return 'Q';
            if (code >= 0x9000 && code <= 0x93ff)
                return 'R';
            if (code >= 0x9400 && code <= 0x97ff)
                return 'S';
            if (code >= 0x9800 && code <= 0x9bff)
                return 'T';
            if (code >= 0x9c00 && code <= 0x9fff)
                return 'U';
            if (code >= 0xa000 && code <= 0xa3ff)
                return 'V';
            if (code >= 0xa400 && code <= 0xa7ff)
                return 'W';
            if (code >= 0xa800 && code <= 0xabff)
                return 'X';
            if (code >= 0xac00 && code <= 0xafff)
                return 'Y';
            return 'Z';
        }
        return Character.toUpperCase(c);
    }

    /**
     * 根据父节点ID生成祖级列表
     *
     * @param parentId 父节点ID
     * @return 祖级列表
     */
    private String getAncestors(Long parentId) {
        SysModule info = moduleMapper.selectModuleById(parentId);
        if (info == null) {
            return "0";
        }
        return info.getAncestors() + "," + info.getModuleId();
    }

    /**
     * 根据模块ID获取模块名称
     *
     * @param moduleId 模块ID
     * @return 模块名称
     */
    private String getModuleNameById(Long moduleId) {
        if (moduleId == null || moduleId == 0) {
            return null;
        }
        SysModule module = moduleMapper.selectModuleById(moduleId);
        return module != null ? module.getModuleName() : null;
    }

    /**
     * 构建模块的唯一键
     *
     * @param moduleName 模块名称
     * @param parentName 父模块名称
     * @return 唯一键
     */
    private String buildModuleKey(String moduleName, String parentName) {
        if (StringUtils.isEmpty(parentName)) {
            return moduleName;
        }
        return parentName + ">" + moduleName;
    }

    /**
     * 查找父模块的键
     *
     * @param parentName     父模块名称
     * @param createdModules 已创建的模块映射
     * @return 父模块的键
     */
    private String findParentModuleKey(String parentName, Map<String, Long> createdModules) {
        // 首先尝试直接匹配（顶级模块）
        if (createdModules.containsKey(parentName)) {
            return parentName;
        }

        // 然后尝试匹配以该名称结尾的键（子模块）
        for (String key : createdModules.keySet()) {
            if (key.endsWith(">" + parentName)) {
                return key;
            }
        }

        return null;
    }
}