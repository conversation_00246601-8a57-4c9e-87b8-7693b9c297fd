{"remainingRequest": "D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ruoyi\\Zhike\\zhike-ui\\src\\views\\system\\module\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\ruoyi\\Zhike\\zhike-ui\\src\\views\\system\\module\\index.vue", "mtime": 1754215759744}, {"path": "D:\\ruoyi\\Zhike\\zhike-ui\\babel.config.js", "mtime": 1737337534038}, {"path": "D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737340298796}, {"path": "D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737340300972}, {"path": "D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737340298796}, {"path": "D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737340301724}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_module", "require", "_competitor", "_vueTreeselect", "_interopRequireDefault", "_pinyinPro", "convertToPinyin", "chinese", "console", "log", "pinyinResult", "pinyin", "toneType", "type", "nonZh", "pattern", "upperResult", "map", "py", "toUpperCase", "finalResult", "join", "_default", "exports", "default", "name", "dicts", "components", "Treeselect", "data", "loading", "formLoading", "moduleList", "moduleOptions", "competitorOptions", "defaultCompetitorId", "title", "open", "isExpandAll", "refreshTable", "queryParams", "moduleName", "undefined", "moduleCode", "moduleType", "status", "competitorId", "nature", "quotationNature", "isBd", "hospitalCertification", "minPrice", "maxPrice", "businessType", "form", "rules", "required", "message", "trigger", "orderNum", "moduleProperty", "filterText", "defaultProps", "children", "label", "currentModule", "showSearch", "expandedKeys", "importOpen", "parseLoading", "importLoading", "fileList", "parsedModules", "selectedModules", "selectedParentModuleId", "watch", "handler", "newVal", "moduleId", "getMaxOrderNum", "val", "$refs", "tree", "filter", "created", "getList", "getCompetitorOptions", "methods", "_this", "saveExpandedKeys", "params", "Object", "assign", "listModule", "then", "response", "handleTree", "$nextTick", "restoreExpandedKeys", "_this2", "listCompetitor", "pageSize", "rows", "normalizer", "node", "length", "id", "filterNode", "value", "toLowerCase", "indexOf", "handleNodeClick", "getParentModuleName", "parentId", "findModule", "list", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "n", "done", "item", "found", "err", "e", "f", "cancel", "reset", "price", "remark", "moduleDesc", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleAdd", "row", "_this3", "_this4", "siblingModules", "maxOrderNum", "Math", "max", "apply", "_toConsumableArray2", "m", "catch", "error", "handleUpdate", "_this5", "$modal", "msgError", "getModule", "clearValidate", "$set", "submitForm", "_this6", "validate", "valid", "updateModule", "msgSuccess", "includes", "push", "refreshModuleDetail", "addModule", "newModuleId", "handleDelete", "_this7", "confirm", "delModule", "deletedModuleId", "_this8", "toggleExpandAll", "_this9", "expandAll", "collapseAll", "$message", "duration", "refreshTree", "_this10", "closeLoading", "setTimeout", "store", "defaultExpandAll", "nodes", "nodesMap", "key", "hasOwnProperty", "expanded", "Number", "_this11", "for<PERSON>ach", "_getAllNodes", "_setExpandedKeys", "handleImport", "resetImport", "upload", "clearFiles", "handleExceed", "msgWarning", "beforeUpload", "file", "isHTML", "endsWith", "isLt10M", "size", "handleParseFile", "_this12", "files", "uploadFiles", "raw", "parseHtmlFile", "code", "msg", "finally", "handleSelectionChange", "selection", "checkSelectable", "index", "handleConfirmImport", "_this13", "importParams", "modules", "parentModuleId", "batchImportModules", "cancelImport"], "sources": ["src/views/system/module/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索区域 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" class=\"mb8\">\n      <el-form-item label=\"模块名称\" prop=\"moduleName\">\n        <el-input\n          v-model=\"queryParams.moduleName\"\n          placeholder=\"请输入模块名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"模块编码\" prop=\"moduleCode\">\n        <el-input\n          v-model=\"queryParams.moduleCode\"\n          placeholder=\"请输入模块编码\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"业务类型\" prop=\"businessType\">\n        <el-select v-model=\"queryParams.businessType\" placeholder=\"请选择业务类型\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.product_plan_type\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"所属厂商\" prop=\"competitorId\">\n        <el-select v-model=\"queryParams.competitorId\" placeholder=\"请选择厂商\" clearable filterable>\n          <el-option\n            v-for=\"item in competitorOptions\"\n            :key=\"item.id\"\n            :label=\"item.name\"\n            :value=\"item.id\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"模块类型\" prop=\"moduleType\">\n        <el-select v-model=\"queryParams.moduleType\" placeholder=\"模块类型\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_module_type\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"模块状态\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_normal_disable\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"模块性质\" prop=\"nature\">\n        <el-select v-model=\"queryParams.nature\" placeholder=\"请选择模块性质\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_module_nature\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"报价类型\" prop=\"quotationNature\">\n        <el-select v-model=\"queryParams.quotationNature\" placeholder=\"请选择报价类型\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_quotation_nature\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"是否BD\" prop=\"isBd\">\n        <el-select v-model=\"queryParams.isBd\" placeholder=\"请选择是否BD\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_yes_no\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"医院评级\" prop=\"hospitalCertification\">\n        <el-select v-model=\"queryParams.hospitalCertification\" placeholder=\"请选择医院评级\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.hospital_certification\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n\n      <el-form-item label=\"价格范围\" prop=\"priceRange\">\n        <el-input-number\n          v-model=\"queryParams.minPrice\"\n          placeholder=\"最小价格\"\n          :min=\"0\"\n          :precision=\"2\"\n          style=\"width: 120px\"\n        />\n        <span style=\"margin: 0 10px\">-</span>\n        <el-input-number\n          v-model=\"queryParams.maxPrice\"\n          placeholder=\"最大价格\"\n          :min=\"0\"\n          :precision=\"2\"\n          style=\"width: 120px\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"20\">\n      <!-- 左侧树形结构 -->\n      <el-col :span=\"6\" class=\"tree-container\">\n        <div class=\"tree-header\">\n          <el-input\n            v-model=\"filterText\"\n            placeholder=\"请输入关键字进行过滤\"\n            clearable\n            prefix-icon=\"el-icon-search\"\n            size=\"small\"\n          />\n          <div class=\"tree-actions\">\n            <el-button\n              type=\"text\"\n              icon=\"el-icon-sort\"\n              size=\"mini\"\n              @click=\"toggleExpandAll\"\n            >{{ isExpandAll ? '全部折叠' : '全部展开' }}</el-button>\n            <el-button\n              type=\"text\"\n              icon=\"el-icon-refresh\"\n              size=\"mini\"\n              @click=\"refreshTree\"\n            >刷新树</el-button>\n            <el-button\n              type=\"text\"\n              icon=\"el-icon-search\"\n              size=\"mini\"\n              @click=\"showSearch = !showSearch\"\n            >{{ showSearch ? '收起搜索' : '高级搜索' }}</el-button>\n          </div>\n        </div>\n        <el-tree\n          ref=\"tree\"\n          :data=\"moduleList\"\n          :props=\"defaultProps\"\n          :filter-node-method=\"filterNode\"\n          :expand-on-click-node=\"false\"\n          node-key=\"moduleId\"\n          :default-expand-all=\"isExpandAll\"\n          highlight-current\n          @node-click=\"handleNodeClick\"\n        >\n          <span class=\"custom-tree-node\" slot-scope=\"{ node, data }\">\n            <span>\n              <i class=\"el-icon-folder\" style=\"margin-right: 5px\"></i>\n              {{ node.label }}\n            </span>\n          </span>\n        </el-tree>\n      </el-col>\n\n      <!-- 右侧内容区域 -->\n      <el-col :span=\"18\">\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span class=\"card-title\">{{ currentModule.moduleName || '请选择模块' }}</span>\n            <el-button-group style=\"float: right\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['system:module:add']\"\n        >新增</el-button>\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-upload2\"\n          size=\"mini\"\n          @click=\"handleImport\"\n          v-hasPermi=\"['system:module:add']\"\n        >导入模块</el-button>\n              <el-button\n                type=\"success\"\n                plain\n                icon=\"el-icon-edit\"\n                size=\"mini\"\n                @click=\"handleUpdate(currentModule)\"\n                v-hasPermi=\"['system:module:edit']\"\n                v-if=\"currentModule.moduleId\"\n              >修改</el-button>\n              <el-button\n                type=\"danger\"\n                plain\n                icon=\"el-icon-delete\"\n                size=\"mini\"\n                @click=\"handleDelete(currentModule)\"\n                v-hasPermi=\"['system:module:remove']\"\n                v-if=\"currentModule.moduleId\"\n              >删除</el-button>\n        <el-button\n          type=\"info\"\n          plain\n                icon=\"el-icon-plus\"\n          size=\"mini\"\n                @click=\"handleAdd(currentModule)\"\n                v-hasPermi=\"['system:module:add']\"\n                v-if=\"currentModule.moduleId\"\n              >添加子模块</el-button>\n            </el-button-group>\n          </div>\n\n          <!-- 模块详情内容 -->\n          <div v-if=\"currentModule.moduleId\" class=\"module-detail\">\n            <el-descriptions :column=\"2\" border>\n              <el-descriptions-item label=\"模块名称\">{{ currentModule.moduleName }}</el-descriptions-item>\n              <el-descriptions-item label=\"模块编码\">{{ currentModule.moduleCode }}</el-descriptions-item>\n              <el-descriptions-item label=\"显示顺序\">{{ currentModule.orderNum }}</el-descriptions-item>\n              <el-descriptions-item label=\"上级模块\">{{ getParentModuleName(currentModule.parentId) }}</el-descriptions-item>\n              <el-descriptions-item label=\"模块类型\">\n                <dict-tag :options=\"dict.type.sys_module_type\" :value=\"currentModule.moduleType\"/>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"模块属性\">\n                <dict-tag :options=\"dict.type.sys_module_property\" :value=\"currentModule.moduleProperty\"/>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"所属厂商\">{{ currentModule.competitorName }}</el-descriptions-item>\n              <el-descriptions-item label=\"模块状态\">\n                <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"currentModule.status\"/>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"性质\">\n                <dict-tag :options=\"dict.type.sys_module_nature\" :value=\"currentModule.nature\"/>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"报价性质\">\n                <dict-tag :options=\"dict.type.sys_quotation_nature\" :value=\"currentModule.quotationNature\"/>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"是否BD\">\n                <dict-tag :options=\"dict.type.sys_yes_no\" :value=\"currentModule.isBd\"/>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"医院评级\">\n                <dict-tag :options=\"dict.type.hospital_certification\" :value=\"currentModule.hospitalCertification\"/>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"模块说明\">{{ currentModule.moduleDesc || '-' }}</el-descriptions-item>\n              <el-descriptions-item label=\"业务类型\">\n                <dict-tag :options=\"dict.type.product_plan_type\" :value=\"currentModule.businessType\"/>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"价格\">\n                <span v-if=\"currentModule.price\">¥{{ currentModule.price }}</span>\n                <span v-else>-</span>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"创建时间\">{{ parseTime(currentModule.createTime) }}</el-descriptions-item>\n              <el-descriptions-item label=\"更新时间\">{{ parseTime(currentModule.updateTime) }}</el-descriptions-item>\n              <el-descriptions-item label=\"备注\" :span=\"2\">{{ currentModule.remark }}</el-descriptions-item>\n            </el-descriptions>\n          </div>\n          <div v-else class=\"empty-tip\">\n            <el-empty description=\"请从左侧选择模块\"></el-empty>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 添加或修改系统模块对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"上级模块\" prop=\"parentId\">\n              <treeselect \n                v-model=\"form.parentId\" \n                :options=\"moduleOptions\" \n                :normalizer=\"normalizer\" \n                placeholder=\"选择上级模块\"\n                :disabled=\"form.parentId === 0\" \n                :clearable=\"false\"\n                :default-expand-level=\"1\"\n                :show-count=\"true\"\n                noOptionsMessage=\"无\"\n                noResultsText=\"无\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"模块名称\" prop=\"moduleName\">\n              <el-input v-model=\"form.moduleName\" placeholder=\"请输入模块名称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"模块编码\" prop=\"moduleCode\">\n              <el-input v-model=\"form.moduleCode\" placeholder=\"请输入模块编码\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"显示排序\" prop=\"orderNum\">\n              <el-input-number v-model=\"form.orderNum\" controls-position=\"right\" :min=\"0\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"模块类型\" prop=\"moduleType\">\n              <el-select v-model=\"form.moduleType\" placeholder=\"请选择\">\n                <el-option\n                  v-for=\"dict in dict.type.sys_module_type\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"模块属性\" prop=\"moduleProperty\">\n              <el-select v-model=\"form.moduleProperty\" placeholder=\"请选择\">\n                <el-option\n                  v-for=\"dict in dict.type.sys_module_property\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"所属厂商\" prop=\"competitorId\">\n              <el-select\n                v-model=\"form.competitorId\"\n                placeholder=\"请选择厂商\"\n                filterable\n                clearable\n              >\n                <el-option\n                  v-for=\"item in competitorOptions\"\n                  :key=\"item.id\"\n                  :label=\"item.name\"\n                  :value=\"item.id\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"模块状态\">\n              <el-radio-group v-model=\"form.status\">\n                <el-radio\n                  v-for=\"dict in dict.type.sys_normal_disable\"\n                  :key=\"dict.value\"\n                  :label=\"dict.value\"\n                >{{dict.label}}</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"性质\" prop=\"nature\">\n              <el-select v-model=\"form.nature\" placeholder=\"请选择\">\n                <el-option\n                  v-for=\"dict in dict.type.sys_module_nature\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"报价性质\" prop=\"quotationNature\">\n              <el-select v-model=\"form.quotationNature\" placeholder=\"请选择\">\n                <el-option\n                  v-for=\"dict in dict.type.sys_quotation_nature\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否BD\" prop=\"isBd\">\n              <el-select v-model=\"form.isBd\" placeholder=\"请选择\">\n                <el-option\n                  v-for=\"dict in dict.type.sys_yes_no\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n                     <el-col :span=\"12\">\n             <el-form-item label=\"医院评级\" prop=\"hospitalCertification\">\n               <el-select v-model=\"form.hospitalCertification\" placeholder=\"请选择\">\n                 <el-option\n                   v-for=\"dict in dict.type.hospital_certification\"\n                   :key=\"dict.value\"\n                   :label=\"dict.label\"\n                   :value=\"dict.value\"\n                 ></el-option>\n               </el-select>\n             </el-form-item>\n           </el-col>\n           <el-col :span=\"12\">\n             <el-form-item label=\"价格\" prop=\"price\">\n               <el-input-number\n                 v-model=\"form.price\"\n                 placeholder=\"请输入价格\"\n                 :min=\"0\"\n                 :precision=\"2\"\n                 controls-position=\"right\"\n                 style=\"width: 100%\"\n               />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"业务类型\" prop=\"businessType\">\n              <el-select v-model=\"form.businessType\" placeholder=\"请选择业务类型\">\n                <el-option\n                  v-for=\"dict in dict.type.product_plan_type\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"模块说明\" prop=\"moduleDesc\">\n              <el-input v-model=\"form.moduleDesc\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入模块说明\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"remark\">\n              <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入备注\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\" :loading=\"formLoading\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 模块导入对话框 -->\n    <el-dialog title=\"模块导入\" :visible.sync=\"importOpen\" width=\"800px\" append-to-body>\n      <div v-if=\"!parsedModules.length\">\n        <el-upload\n          ref=\"upload\"\n          action=\"#\"\n          :limit=\"1\"\n          accept=\".html\"\n          :on-exceed=\"handleExceed\"\n          :before-upload=\"beforeUpload\"\n          :auto-upload=\"false\"\n          :file-list=\"fileList\"\n          drag\n        >\n          <i class=\"el-icon-upload\"></i>\n          <div class=\"el-upload__text\">将HTML文件拖到此处，或<em>点击上传</em></div>\n          <div class=\"el-upload__tip\" slot=\"tip\">只能上传HTML文件，且不超过10MB</div>\n        </el-upload>\n        <div style=\"margin-top: 20px; text-align: center\">\n          <el-button type=\"primary\" @click=\"handleParseFile\" :loading=\"parseLoading\">解析文件</el-button>\n        </div>\n      </div>\n\n      <div v-else>\n        <div class=\"import-header\">\n          <span>解析结果：共找到 {{ parsedModules.length }} 个模块</span>\n          <el-button type=\"text\" @click=\"resetImport\">重新选择文件</el-button>\n        </div>\n\n        <!-- 父模块选择 -->\n        <div class=\"parent-module-selector\">\n          <el-form :inline=\"true\">\n            <el-form-item label=\"导入到父模块：\">\n              <treeselect\n                v-model=\"selectedParentModuleId\"\n                :options=\"moduleOptions\"\n                :normalizer=\"normalizer\"\n                :show-count=\"true\"\n                placeholder=\"选择父模块（可选）\"\n                :clearable=\"true\"\n                style=\"width: 300px\"\n              />\n            </el-form-item>\n          </el-form>\n        </div>\n\n        <el-table\n          ref=\"importTable\"\n          :data=\"parsedModules\"\n          border\n          style=\"width: 100%; margin-top: 10px\"\n          max-height=\"400\"\n          @selection-change=\"handleSelectionChange\"\n        >\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" :selectable=\"checkSelectable\" />\n          <el-table-column prop=\"moduleName\" label=\"模块名称\" min-width=\"200\">\n            <template slot-scope=\"scope\">\n              <span\n                :style=\"{\n                  'margin-left': (scope.row.level - 1) * 20 + 'px',\n                  'font-weight': scope.row.isParent ? 'bold' : 'normal',\n                  'color': scope.row.isParent ? '#409EFF' : '#606266'\n                }\"\n              >\n                <i v-if=\"scope.row.isParent\" class=\"el-icon-folder\" style=\"margin-right: 5px\"></i>\n                <i v-else class=\"el-icon-document\" style=\"margin-right: 5px\"></i>\n                {{ scope.row.moduleName }}\n              </span>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"moduleCode\" label=\"模块编码\" width=\"150\" />\n          <el-table-column prop=\"parentName\" label=\"父模块\" width=\"150\">\n            <template slot-scope=\"scope\">\n              {{ scope.row.parentName || '无' }}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"level\" label=\"层级\" width=\"60\">\n            <template slot-scope=\"scope\">\n              <el-tag :type=\"scope.row.level === 1 ? 'primary' : scope.row.level === 2 ? 'success' : 'warning'\" size=\"mini\">\n                {{ scope.row.level || 1 }}级\n              </el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"orderNum\" label=\"排序\" width=\"80\" />\n        </el-table>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button v-if=\"parsedModules.length\" type=\"primary\" @click=\"handleConfirmImport\" :loading=\"importLoading\">\n          确认导入\n        </el-button>\n        <el-button @click=\"cancelImport\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listModule, getModule, delModule, addModule, updateModule, parseHtmlFile, batchImportModules } from \"@/api/system/module\";\nimport { listCompetitor } from \"@/api/competitor/competitor\";\nimport Treeselect from \"@riophae/vue-treeselect\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\nimport { pinyin } from 'pinyin-pro';\n\n// 添加汉字转拼音的工具函数\nfunction convertToPinyin(chinese) {\n  if (!chinese) return '';\n  console.log('输入的中文：', chinese);\n  \n  // 将汉字转换为拼音，并转换为大写\n  const pinyinResult = pinyin(chinese, {\n    toneType: 'none',    // 不带声调\n    type: 'array',       // 返回拼音数组\n    nonZh: 'consecutive', // 非汉字连续返回\n    pattern: 'first'     // 只取首字母\n  });\n  console.log('pinyin转换结果：', pinyinResult);\n  \n  const upperResult = pinyinResult.map(py => py.toUpperCase());\n  console.log('转大写结果：', upperResult);\n  \n  const finalResult = upperResult.join('');\n  console.log('最终结果：', finalResult);\n  \n  return finalResult;\n}\n\nexport default {\n  name: \"Module\",\n  dicts: ['sys_normal_disable', 'sys_module_type', 'sys_module_property', 'sys_module_nature', 'sys_quotation_nature', 'sys_yes_no', 'hospital_certification', 'product_plan_type'],\n  components: { Treeselect },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 表单加载状态\n      formLoading: false,\n      // 表格树数据\n      moduleList: [],\n      // 模块树选项\n      moduleOptions: [],\n      // 厂商选项\n      competitorOptions: [],\n      // 默认卫宁厂商ID\n      defaultCompetitorId: null,\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 是否展开，默认全部展开\n      isExpandAll: false,\n      // 重新渲染表格状态\n      refreshTable: true,\n      // 查询参数\n      queryParams: {\n        moduleName: undefined,\n        moduleCode: undefined,\n        moduleType: undefined,\n        status: undefined,\n        competitorId: undefined,\n        nature: undefined,\n        quotationNature: undefined,\n        isBd: undefined,\n        hospitalCertification: undefined,\n        minPrice: undefined,\n        maxPrice: undefined,\n        businessType: undefined\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        moduleName: [\n          { required: true, message: \"模块名称不能为空\", trigger: \"blur\" }\n        ],\n        orderNum: [\n          { required: true, message: \"显示排序不能为空\", trigger: \"blur\" }\n        ],\n        moduleType: [\n          { required: true, message: \"模块类型不能为空\", trigger: \"blur\" }\n        ],\n        moduleProperty: [\n          { required: true, message: \"模块属性不能为空\", trigger: \"blur\" }\n        ],\n        competitorId: [\n          { required: true, message: \"请选择所属厂商\", trigger: \"change\" }\n        ],\n        businessType: [\n          { required: true, message: \"请选择业务类型\", trigger: \"change\" }\n        ]\n      },\n      // 树形结构相关\n      filterText: '',\n      defaultProps: {\n        children: 'children',\n        label: 'moduleName'\n      },\n      currentModule: {}, // 当前选中的模块\n      showSearch: false, // 默认不显示搜索条件\n      expandedKeys: [], // 当前展开的节点ID列表\n      // 导入相关\n      importOpen: false, // 导入对话框是否显示\n      parseLoading: false, // 解析加载状态\n      importLoading: false, // 导入加载状态\n      fileList: [], // 上传文件列表\n      parsedModules: [], // 解析出的模块数据\n      selectedModules: [], // 选中的模块数据\n      selectedParentModuleId: null, // 选择的父模块ID\n    };\n  },\n  watch: {\n    // 监听模块名称变化，自动生成模块编码\n    'form.moduleName': {\n      handler(newVal) {\n        // 在新增或编辑时都自动生成编码\n        if (newVal) {\n          console.log('模块名称变化：', newVal);\n          this.form.moduleCode = convertToPinyin(newVal);\n          console.log('生成的模块编码：', this.form.moduleCode);\n        }\n      }\n    },\n    // 监听上级模块变化，重新获取显示排序\n    'form.parentId': {\n      handler(newVal) {\n        // 如果是新增模式（没有moduleId）并且选择了有效的上级模块\n        if (!this.form.moduleId && newVal !== undefined) {\n          this.getMaxOrderNum(newVal);\n        }\n      }\n    },\n    // 监听过滤文本变化\n    filterText(val) {\n      this.$refs.tree.filter(val);\n    }\n  },\n  created() {\n    this.getList();\n    this.getCompetitorOptions();\n  },\n  methods: {\n    /** 查询模块列表 */\n    getList() {\n      this.loading = true;\n      \n      // 保存当前展开的节点ID\n      this.saveExpandedKeys();\n      \n      // 确保将厂商ID传递给后端接口\n      let params = Object.assign({}, this.queryParams);\n      \n      listModule(params).then(response => {\n        this.moduleList = this.handleTree(response.data, \"moduleId\");\n        this.loading = false;\n        \n        // 恢复展开的节点\n        this.$nextTick(() => {\n          this.restoreExpandedKeys();\n        });\n      });\n    },\n    /** 查询厂商选项 */\n    getCompetitorOptions() {\n      listCompetitor({ status: '0', pageSize: 9999 }).then(response => {\n        this.competitorOptions = response.rows;\n      });\n    },\n    /** 转换模块数据结构 */\n    normalizer(node) {\n      if (node.children && !node.children.length) {\n        delete node.children;\n      }\n      return {\n        id: node.moduleId,\n        label: node.moduleName,\n        children: node.children\n      };\n    },\n    // 过滤节点\n    filterNode(value, data) {\n      if (!value) return true;\n      return data.moduleName.toLowerCase().indexOf(value.toLowerCase()) !== -1;\n    },\n    // 节点点击事件\n    handleNodeClick(data) {\n      this.currentModule = data;\n    },\n    // 获取父模块名称\n    getParentModuleName(parentId) {\n      if (!parentId || parentId === 0) {\n        return '无';\n      }\n      \n      // 递归在模块树中查找指定ID的模块\n      const findModule = (list, id) => {\n        for (const item of list) {\n          if (item.moduleId === id) {\n            return item.moduleName;\n          }\n          if (item.children && item.children.length > 0) {\n            const found = findModule(item.children, id);\n            if (found) return found;\n          }\n        }\n        return null;\n      };\n      \n      const name = findModule(this.moduleList, parentId);\n      return name || '未知';\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        moduleId: undefined,\n        parentId: undefined,\n        moduleName: undefined,\n        moduleCode: undefined,\n        orderNum: 99, // 设置默认排序为99\n        moduleType: \"1\",  // 修改默认值为系统模块\n        moduleProperty: \"3\", // 默认为功能\n        competitorId: undefined, // 取消默认厂商设置\n        status: \"0\",\n        nature: undefined,\n        quotationNature: undefined,\n        isBd: undefined,\n        hospitalCertification: undefined,\n        price: undefined,\n        remark: undefined,\n        moduleDesc: undefined,\n        businessType: undefined\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    /** 新增按钮操作 */\n    handleAdd(row) {\n      this.reset();\n      if (row && row.moduleId) {\n        this.form.parentId = row.moduleId;\n        // 如果是子模块，继承父模块的厂商ID\n        if (row.competitorId) {\n          this.form.competitorId = row.competitorId;\n        }\n        \n        // 获取同级模块的最大排序号\n        this.getMaxOrderNum(row.moduleId);\n      } else {\n        // 获取顶级模块的最大排序号\n        this.getMaxOrderNum(0);\n      }\n      this.open = true;\n      this.title = \"添加模块\";\n      listModule().then(response => {\n        this.moduleOptions = this.handleTree(response.data, \"moduleId\");\n      });\n    },\n    /** 获取同级模块中的最大排序号 */\n    getMaxOrderNum(parentId) {\n      // 明确查询同一父模块下的直接子模块\n      listModule({ parentId: parentId }).then(response => {\n        if (response.data && response.data.length > 0) {\n          // 过滤出具有相同parentId的模块，确保只处理真正的同级模块\n          const siblingModules = response.data.filter(item => item.parentId === parentId);\n          \n          if (siblingModules.length > 0) {\n            // 找出同级模块中的最大排序号\n            const maxOrderNum = Math.max(...siblingModules.map(m => m.orderNum || 0));\n            // 设置新模块的排序号为最大值+1\n            this.form.orderNum = maxOrderNum + 1;\n            console.log('获取到同级模块最大排序号：', maxOrderNum, '设置新模块排序号为：', this.form.orderNum);\n          } else {\n            // 如果该父模块下没有子模块，则设置排序号为1\n            this.form.orderNum = 1;\n            console.log('该父模块下无子模块，设置新模块排序号为：1');\n          }\n        } else {\n          // 如果没有返回数据，则设置排序号为1\n          this.form.orderNum = 1;\n          console.log('查询无返回数据，设置新模块排序号为：1');\n        }\n      }).catch(error => {\n        console.error('获取同级模块排序号失败：', error);\n        // 发生错误时设置默认排序号\n        this.form.orderNum = 1;\n      });\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      if (!row || !row.moduleId) {\n        this.$modal.msgError(\"请先选择要修改的模块\");\n        return;\n      }\n      \n      getModule(row.moduleId).then(response => {\n        this.form = response.data;\n        // 如果是顶级模块(parentId=0),则禁用上级模块选择\n        if (this.form.parentId === 0) {\n          this.$nextTick(() => {\n            this.$refs.form.clearValidate('parentId');\n            this.$set(this.rules, 'parentId', []);\n          });\n          // 添加一个顶级节点选项\n          this.moduleOptions = [{\n            id: 0,\n            label: '无'\n          }];\n        } else {\n          listModule().then(response => {\n            this.moduleOptions = this.handleTree(response.data, \"moduleId\");\n          });\n        }\n        this.open = true;\n        this.title = \"修改模块\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm: function() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          this.formLoading = true;\n          \n          // 保存当前展开的节点ID\n          this.saveExpandedKeys();\n          \n          // 记录当前表单的父节点ID和本身ID\n          const parentId = this.form.parentId;\n          const moduleId = this.form.moduleId;\n          \n          if (moduleId != undefined) {\n            updateModule(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              \n              // 将当前节点ID添加到展开节点列表\n              if (!this.expandedKeys.includes(moduleId) && moduleId !== 0) {\n                this.expandedKeys.push(moduleId);\n              }\n              \n              // 将父节点ID添加到展开节点列表\n              if (parentId && !this.expandedKeys.includes(parentId) && parentId !== 0) {\n                this.expandedKeys.push(parentId);\n              }\n              \n              this.getList();\n              \n              // 如果是当前模块，也要刷新详情\n              if (this.currentModule.moduleId === moduleId) {\n                this.refreshModuleDetail(moduleId);\n              }\n              \n              this.formLoading = false;\n            }).catch(() => {\n              this.formLoading = false;\n            });\n          } else {\n            addModule(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              \n              // 确保父节点在展开列表中\n              if (parentId && !this.expandedKeys.includes(parentId) && parentId !== 0) {\n                this.expandedKeys.push(parentId);\n              }\n              \n              // 如果是新增的根节点，记录其ID\n              if (response.data && response.data.moduleId && parentId === 0) {\n                const newModuleId = response.data.moduleId;\n                this.expandedKeys.push(newModuleId);\n              }\n              \n              this.getList();\n              this.formLoading = false;\n            }).catch(() => {\n              this.formLoading = false;\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      if (!row || !row.moduleId) {\n        this.$modal.msgError(\"未选择要删除的模块\");\n        return;\n      }\n      \n      // 检查是否有子节点\n      if (row.children && row.children.length > 0) {\n        this.$modal.msgError('该模块下存在子模块，请先删除子模块');\n        return;\n      }\n      \n      this.$modal.confirm('确定要删除模块【' + row.moduleName + '】吗？删除后无法恢复！').then(() => {\n        return delModule(row.moduleId);\n      }).then(() => {\n        // 记录当前删除的模块ID\n        const deletedModuleId = row.moduleId;\n        \n        // 刷新模块列表\n        this.getList();\n        \n        // 如果右侧当前显示的就是被删除的模块，则清空右侧内容\n        if (this.currentModule && this.currentModule.moduleId === deletedModuleId) {\n          this.currentModule = {};\n        }\n        \n        this.$modal.msgSuccess(\"模块【\" + row.moduleName + \"】删除成功\");\n      }).catch(() => {});\n    },\n    // 刷新模块详情\n    refreshModuleDetail(moduleId) {\n      if (!moduleId) return;\n      \n      // 获取最新的模块详情数据\n      getModule(moduleId).then(response => {\n        if (response.data) {\n          // 更新当前模块对象\n          this.currentModule = response.data;\n        }\n      }).catch(error => {\n        console.error(\"获取模块详情失败\", error);\n      });\n    },\n    // 切换全部展开/折叠\n    toggleExpandAll() {\n      this.isExpandAll = !this.isExpandAll;\n      this.$nextTick(() => {\n        if (this.isExpandAll) {\n          this.$refs.tree.expandAll();\n        } else {\n          this.$refs.tree.collapseAll();\n        }\n        \n        // 更新展开节点状态\n        this.saveExpandedKeys();\n        \n        // 显示成功提示\n        this.$message({\n          message: this.isExpandAll ? '已全部展开' : '已全部折叠',\n          type: 'success',\n          duration: 1500\n        });\n      });\n    },\n    /** 刷新树结构 */\n    refreshTree() {\n      console.log('刷新模块树结构');\n      // 显示加载中提示\n      this.$modal.loading(\"正在刷新树结构，请稍候...\");\n      \n      // 重新获取模块数据\n      listModule(this.queryParams).then(response => {\n        // 处理树形结构数据\n        this.moduleList = this.handleTree(response.data, \"moduleId\");\n        this.loading = false;\n        \n        // 关闭加载提示并显示成功消息\n        this.$modal.closeLoading();\n        this.$modal.msgSuccess(\"树结构刷新成功\");\n        \n        // 保持树的展开状态 - 放在成功提示之后延时执行，避免卡顿\n        setTimeout(() => {\n          if (this.$refs.tree) {\n            // 设置默认展开状态\n            this.$refs.tree.store.defaultExpandAll = this.isExpandAll;\n            \n            // 如果当前有选中的模块，刷新其详情\n            if (this.currentModule && this.currentModule.moduleId) {\n              this.refreshModuleDetail(this.currentModule.moduleId);\n            }\n          }\n        }, 100);\n      }).catch(error => {\n        console.error(\"刷新模块树结构失败:\", error);\n        this.$modal.closeLoading();\n        this.$modal.msgError(\"刷新树结构失败\");\n      });\n    },\n    // 保存当前所有展开节点的ID\n    saveExpandedKeys() {\n      if (this.$refs.tree) {\n        const expandedKeys = [];\n        \n        // 遍历所有节点，记录已展开的节点ID\n        const nodes = this.$refs.tree.store.nodesMap;\n        for (const key in nodes) {\n          if (nodes.hasOwnProperty(key) && nodes[key].expanded) {\n            expandedKeys.push(Number(key));\n          }\n        }\n        \n        this.expandedKeys = expandedKeys;\n        console.log('保存展开节点状态:', this.expandedKeys);\n      }\n    },\n    // 恢复节点展开状态\n    restoreExpandedKeys() {\n      if (this.$refs.tree) {\n        // 如果是展开全部的状态，全部展开\n        if (this.isExpandAll) {\n          this.$refs.tree.expandAll();\n          return;\n        }\n        \n        // 如果有记录展开节点，则恢复这些节点的展开状态\n        if (this.expandedKeys && this.expandedKeys.length > 0) {\n          console.log('恢复展开节点状态:', this.expandedKeys);\n          const nodes = this.$refs.tree.store.nodesMap;\n          \n          // 模拟点击每个保存的展开节点，触发展开\n          this.expandedKeys.forEach(key => {\n            if (nodes[key]) {\n              nodes[key].expanded = true;\n            }\n          });\n          \n          // 强制更新树视图\n          this.$refs.tree.store._getAllNodes().forEach(node => {\n            this.$refs.tree.store._setExpandedKeys(node);\n          });\n        }\n      }\n    },\n    // 导入模块\n    handleImport() {\n      this.importOpen = true;\n      this.resetImport();\n    },\n    // 重置导入状态\n    resetImport() {\n      this.fileList = [];\n      this.parsedModules = [];\n      this.selectedModules = [];\n      this.selectedParentModuleId = null;\n      this.parseLoading = false;\n      this.importLoading = false;\n      this.$refs.upload && this.$refs.upload.clearFiles();\n    },\n    // 文件数量超出限制\n    handleExceed() {\n      this.$modal.msgWarning('只能选择一个HTML文件');\n    },\n    // 上传前检查\n    beforeUpload(file) {\n      const isHTML = file.type === 'text/html' || file.name.toLowerCase().endsWith('.html');\n      const isLt10M = file.size / 1024 / 1024 < 10;\n\n      if (!isHTML) {\n        this.$modal.msgError('只能上传HTML文件!');\n        return false;\n      }\n      if (!isLt10M) {\n        this.$modal.msgError('上传文件大小不能超过 10MB!');\n        return false;\n      }\n      return false; // 阻止自动上传\n    },\n    // 解析文件\n    handleParseFile() {\n      const files = this.$refs.upload.uploadFiles;\n      if (!files || files.length === 0) {\n        this.$modal.msgWarning('请先选择HTML文件');\n        return;\n      }\n\n      this.parseLoading = true;\n      const file = files[0].raw;\n\n      parseHtmlFile(file).then(response => {\n        if (response.code === 200) {\n          this.parsedModules = response.data;\n          this.$modal.msgSuccess('文件解析成功，共解析出 ' + this.parsedModules.length + ' 个模块');\n        } else {\n          this.$modal.msgError(response.msg || '解析失败');\n        }\n      }).catch(error => {\n        console.error('解析文件失败:', error);\n        this.$modal.msgError('解析文件失败');\n      }).finally(() => {\n        this.parseLoading = false;\n      });\n    },\n    // 表格选择变化\n    handleSelectionChange(selection) {\n      this.selectedModules = selection;\n    },\n    // 检查行是否可选择\n    checkSelectable(row, index) {\n      return true; // 所有行都可选择\n    },\n    // 确认导入\n    handleConfirmImport() {\n      if (!this.parsedModules || this.parsedModules.length === 0) {\n        this.$modal.msgWarning('没有可导入的模块数据');\n        return;\n      }\n\n      // 获取选中的模块\n      const selectedModules = this.selectedModules && this.selectedModules.length > 0 ?\n        this.selectedModules : this.parsedModules;\n\n      if (!selectedModules || selectedModules.length === 0) {\n        this.$modal.msgWarning('请选择要导入的模块');\n        return;\n      }\n\n      this.$modal.confirm('确定要导入选中的 ' + selectedModules.length + ' 个模块吗？').then(() => {\n        this.importLoading = true;\n\n        // 构建导入参数\n        const importParams = {\n          modules: selectedModules,\n          parentModuleId: this.selectedParentModuleId\n        };\n\n        batchImportModules(importParams).then(response => {\n          if (response.code === 200) {\n            this.$modal.msgSuccess(response.msg || '导入成功');\n            this.importOpen = false;\n            this.getList(); // 刷新模块列表\n          } else {\n            this.$modal.msgError(response.msg || '导入失败');\n          }\n        }).catch(error => {\n          console.error('导入模块失败:', error);\n          this.$modal.msgError('导入模块失败');\n        }).finally(() => {\n          this.importLoading = false;\n        });\n      });\n    },\n    // 取消导入\n    cancelImport() {\n      this.importOpen = false;\n      this.resetImport();\n    }\n  }\n};\n</script> \n\n<style scoped>\n.mb8 {\n  margin-bottom: 8px;\n}\n\n.tree-container {\n  background-color: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  height: calc(100vh - 130px);\n  overflow: auto;\n}\n\n.tree-header {\n  padding: 10px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.tree-actions {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 8px;\n}\n\n.module-detail {\n  margin-top: 10px;\n}\n\n.empty-tip {\n  padding: 40px 0;\n  text-align: center;\n}\n\n.custom-tree-node {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  font-size: 14px;\n  padding-right: 8px;\n}\n\n.card-title {\n  font-size: 15px;\n  font-weight: bold;\n}\n\n.import-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n  padding: 10px;\n}\n\n.parent-module-selector {\n  margin-bottom: 15px;\n  padding: 10px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n</style> "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AA0jBA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAC,sBAAA,CAAAH,OAAA;AACAA,OAAA;AACA,IAAAI,UAAA,GAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA,SAAAK,gBAAAC,OAAA;EACA,KAAAA,OAAA;EACAC,OAAA,CAAAC,GAAA,WAAAF,OAAA;;EAEA;EACA,IAAAG,YAAA,OAAAC,iBAAA,EAAAJ,OAAA;IACAK,QAAA;IAAA;IACAC,IAAA;IAAA;IACAC,KAAA;IAAA;IACAC,OAAA;EACA;EACAP,OAAA,CAAAC,GAAA,gBAAAC,YAAA;EAEA,IAAAM,WAAA,GAAAN,YAAA,CAAAO,GAAA,WAAAC,EAAA;IAAA,OAAAA,EAAA,CAAAC,WAAA;EAAA;EACAX,OAAA,CAAAC,GAAA,WAAAO,WAAA;EAEA,IAAAI,WAAA,GAAAJ,WAAA,CAAAK,IAAA;EACAb,OAAA,CAAAC,GAAA,UAAAW,WAAA;EAEA,OAAAA,WAAA;AACA;AAAA,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA;EACAC,KAAA;EACAC,UAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,WAAA;MACA;MACAC,UAAA;MACA;MACAC,aAAA;MACA;MACAC,iBAAA;MACA;MACAC,mBAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;MACA;MACAC,YAAA;MACA;MACAC,WAAA;QACAC,UAAA,EAAAC,SAAA;QACAC,UAAA,EAAAD,SAAA;QACAE,UAAA,EAAAF,SAAA;QACAG,MAAA,EAAAH,SAAA;QACAI,YAAA,EAAAJ,SAAA;QACAK,MAAA,EAAAL,SAAA;QACAM,eAAA,EAAAN,SAAA;QACAO,IAAA,EAAAP,SAAA;QACAQ,qBAAA,EAAAR,SAAA;QACAS,QAAA,EAAAT,SAAA;QACAU,QAAA,EAAAV,SAAA;QACAW,YAAA,EAAAX;MACA;MACA;MACAY,IAAA;MACA;MACAC,KAAA;QACAd,UAAA,GACA;UAAAe,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAd,UAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,cAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAZ,YAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,YAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAG,UAAA;MACAC,YAAA;QACAC,QAAA;QACAC,KAAA;MACA;MACAC,aAAA;MAAA;MACAC,UAAA;MAAA;MACAC,YAAA;MAAA;MACA;MACAC,UAAA;MAAA;MACAC,YAAA;MAAA;MACAC,aAAA;MAAA;MACAC,QAAA;MAAA;MACAC,aAAA;MAAA;MACAC,eAAA;MAAA;MACAC,sBAAA;IACA;EACA;EACAC,KAAA;IACA;IACA;MACAC,OAAA,WAAAA,QAAAC,MAAA;QACA;QACA,IAAAA,MAAA;UACArE,OAAA,CAAAC,GAAA,YAAAoE,MAAA;UACA,KAAAvB,IAAA,CAAAX,UAAA,GAAArC,eAAA,CAAAuE,MAAA;UACArE,OAAA,CAAAC,GAAA,kBAAA6C,IAAA,CAAAX,UAAA;QACA;MACA;IACA;IACA;IACA;MACAiC,OAAA,WAAAA,QAAAC,MAAA;QACA;QACA,UAAAvB,IAAA,CAAAwB,QAAA,IAAAD,MAAA,KAAAnC,SAAA;UACA,KAAAqC,cAAA,CAAAF,MAAA;QACA;MACA;IACA;IACA;IACAhB,UAAA,WAAAA,WAAAmB,GAAA;MACA,KAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA,CAAAH,GAAA;IACA;EACA;EACAI,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,oBAAA;EACA;EACAC,OAAA;IACA,aACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,KAAA1D,OAAA;;MAEA;MACA,KAAA2D,gBAAA;;MAEA;MACA,IAAAC,MAAA,GAAAC,MAAA,CAAAC,MAAA,UAAApD,WAAA;MAEA,IAAAqD,kBAAA,EAAAH,MAAA,EAAAI,IAAA,WAAAC,QAAA;QACAP,KAAA,CAAAxD,UAAA,GAAAwD,KAAA,CAAAQ,UAAA,CAAAD,QAAA,CAAAlE,IAAA;QACA2D,KAAA,CAAA1D,OAAA;;QAEA;QACA0D,KAAA,CAAAS,SAAA;UACAT,KAAA,CAAAU,mBAAA;QACA;MACA;IACA;IACA,aACAZ,oBAAA,WAAAA,qBAAA;MAAA,IAAAa,MAAA;MACA,IAAAC,0BAAA;QAAAvD,MAAA;QAAAwD,QAAA;MAAA,GAAAP,IAAA,WAAAC,QAAA;QACAI,MAAA,CAAAjE,iBAAA,GAAA6D,QAAA,CAAAO,IAAA;MACA;IACA;IACA,eACAC,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAA,IAAA,CAAAzC,QAAA,KAAAyC,IAAA,CAAAzC,QAAA,CAAA0C,MAAA;QACA,OAAAD,IAAA,CAAAzC,QAAA;MACA;MACA;QACA2C,EAAA,EAAAF,IAAA,CAAA1B,QAAA;QACAd,KAAA,EAAAwC,IAAA,CAAA/D,UAAA;QACAsB,QAAA,EAAAyC,IAAA,CAAAzC;MACA;IACA;IACA;IACA4C,UAAA,WAAAA,WAAAC,KAAA,EAAA/E,IAAA;MACA,KAAA+E,KAAA;MACA,OAAA/E,IAAA,CAAAY,UAAA,CAAAoE,WAAA,GAAAC,OAAA,CAAAF,KAAA,CAAAC,WAAA;IACA;IACA;IACAE,eAAA,WAAAA,gBAAAlF,IAAA;MACA,KAAAoC,aAAA,GAAApC,IAAA;IACA;IACA;IACAmF,mBAAA,WAAAA,oBAAAC,QAAA;MACA,KAAAA,QAAA,IAAAA,QAAA;QACA;MACA;;MAEA;MACA,IAAAC,WAAA,YAAAA,WAAAC,IAAA,EAAAT,EAAA;QAAA,IAAAU,SAAA,OAAAC,2BAAA,CAAA7F,OAAA,EACA2F,IAAA;UAAAG,KAAA;QAAA;UAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;YAAA,IAAAC,IAAA,GAAAJ,KAAA,CAAAV,KAAA;YACA,IAAAc,IAAA,CAAA5C,QAAA,KAAA4B,EAAA;cACA,OAAAgB,IAAA,CAAAjF,UAAA;YACA;YACA,IAAAiF,IAAA,CAAA3D,QAAA,IAAA2D,IAAA,CAAA3D,QAAA,CAAA0C,MAAA;cACA,IAAAkB,KAAA,GAAAT,WAAA,CAAAQ,IAAA,CAAA3D,QAAA,EAAA2C,EAAA;cACA,IAAAiB,KAAA,SAAAA,KAAA;YACA;UACA;QAAA,SAAAC,GAAA;UAAAR,SAAA,CAAAS,CAAA,CAAAD,GAAA;QAAA;UAAAR,SAAA,CAAAU,CAAA;QAAA;QACA;MACA;MAEA,IAAArG,IAAA,GAAAyF,WAAA,MAAAlF,UAAA,EAAAiF,QAAA;MACA,OAAAxF,IAAA;IACA;IACA;IACAsG,MAAA,WAAAA,OAAA;MACA,KAAA1F,IAAA;MACA,KAAA2F,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA1E,IAAA;QACAwB,QAAA,EAAApC,SAAA;QACAuE,QAAA,EAAAvE,SAAA;QACAD,UAAA,EAAAC,SAAA;QACAC,UAAA,EAAAD,SAAA;QACAiB,QAAA;QAAA;QACAf,UAAA;QAAA;QACAgB,cAAA;QAAA;QACAd,YAAA,EAAAJ,SAAA;QAAA;QACAG,MAAA;QACAE,MAAA,EAAAL,SAAA;QACAM,eAAA,EAAAN,SAAA;QACAO,IAAA,EAAAP,SAAA;QACAQ,qBAAA,EAAAR,SAAA;QACAuF,KAAA,EAAAvF,SAAA;QACAwF,MAAA,EAAAxF,SAAA;QACAyF,UAAA,EAAAzF,SAAA;QACAW,YAAA,EAAAX;MACA;MACA,KAAA0F,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAhD,OAAA;IACA;IACA,aACAiD,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA,aACAE,SAAA,WAAAA,UAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAT,KAAA;MACA,IAAAQ,GAAA,IAAAA,GAAA,CAAA1D,QAAA;QACA,KAAAxB,IAAA,CAAA2D,QAAA,GAAAuB,GAAA,CAAA1D,QAAA;QACA;QACA,IAAA0D,GAAA,CAAA1F,YAAA;UACA,KAAAQ,IAAA,CAAAR,YAAA,GAAA0F,GAAA,CAAA1F,YAAA;QACA;;QAEA;QACA,KAAAiC,cAAA,CAAAyD,GAAA,CAAA1D,QAAA;MACA;QACA;QACA,KAAAC,cAAA;MACA;MACA,KAAA1C,IAAA;MACA,KAAAD,KAAA;MACA,IAAAyD,kBAAA,IAAAC,IAAA,WAAAC,QAAA;QACA0C,MAAA,CAAAxG,aAAA,GAAAwG,MAAA,CAAAzC,UAAA,CAAAD,QAAA,CAAAlE,IAAA;MACA;IACA;IACA,oBACAkD,cAAA,WAAAA,eAAAkC,QAAA;MAAA,IAAAyB,MAAA;MACA;MACA,IAAA7C,kBAAA;QAAAoB,QAAA,EAAAA;MAAA,GAAAnB,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAlE,IAAA,IAAAkE,QAAA,CAAAlE,IAAA,CAAA4E,MAAA;UACA;UACA,IAAAkC,cAAA,GAAA5C,QAAA,CAAAlE,IAAA,CAAAsD,MAAA,WAAAuC,IAAA;YAAA,OAAAA,IAAA,CAAAT,QAAA,KAAAA,QAAA;UAAA;UAEA,IAAA0B,cAAA,CAAAlC,MAAA;YACA;YACA,IAAAmC,WAAA,GAAAC,IAAA,CAAAC,GAAA,CAAAC,KAAA,CAAAF,IAAA,MAAAG,mBAAA,CAAAxH,OAAA,EAAAmH,cAAA,CAAA1H,GAAA,WAAAgI,CAAA;cAAA,OAAAA,CAAA,CAAAtF,QAAA;YAAA;YACA;YACA+E,MAAA,CAAApF,IAAA,CAAAK,QAAA,GAAAiF,WAAA;YACApI,OAAA,CAAAC,GAAA,kBAAAmI,WAAA,gBAAAF,MAAA,CAAApF,IAAA,CAAAK,QAAA;UACA;YACA;YACA+E,MAAA,CAAApF,IAAA,CAAAK,QAAA;YACAnD,OAAA,CAAAC,GAAA;UACA;QACA;UACA;UACAiI,MAAA,CAAApF,IAAA,CAAAK,QAAA;UACAnD,OAAA,CAAAC,GAAA;QACA;MACA,GAAAyI,KAAA,WAAAC,KAAA;QACA3I,OAAA,CAAA2I,KAAA,iBAAAA,KAAA;QACA;QACAT,MAAA,CAAApF,IAAA,CAAAK,QAAA;MACA;IACA;IACA,aACAyF,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,KAAArB,KAAA;MACA,KAAAQ,GAAA,KAAAA,GAAA,CAAA1D,QAAA;QACA,KAAAwE,MAAA,CAAAC,QAAA;QACA;MACA;MAEA,IAAAC,iBAAA,EAAAhB,GAAA,CAAA1D,QAAA,EAAAgB,IAAA,WAAAC,QAAA;QACAsD,MAAA,CAAA/F,IAAA,GAAAyC,QAAA,CAAAlE,IAAA;QACA;QACA,IAAAwH,MAAA,CAAA/F,IAAA,CAAA2D,QAAA;UACAoC,MAAA,CAAApD,SAAA;YACAoD,MAAA,CAAApE,KAAA,CAAA3B,IAAA,CAAAmG,aAAA;YACAJ,MAAA,CAAAK,IAAA,CAAAL,MAAA,CAAA9F,KAAA;UACA;UACA;UACA8F,MAAA,CAAApH,aAAA;YACAyE,EAAA;YACA1C,KAAA;UACA;QACA;UACA,IAAA6B,kBAAA,IAAAC,IAAA,WAAAC,QAAA;YACAsD,MAAA,CAAApH,aAAA,GAAAoH,MAAA,CAAArD,UAAA,CAAAD,QAAA,CAAAlE,IAAA;UACA;QACA;QACAwH,MAAA,CAAAhH,IAAA;QACAgH,MAAA,CAAAjH,KAAA;MACA;IACA;IACA;IACAuH,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAA3E,KAAA,SAAA4E,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAF,MAAA,CAAA7H,WAAA;;UAEA;UACA6H,MAAA,CAAAnE,gBAAA;;UAEA;UACA,IAAAwB,QAAA,GAAA2C,MAAA,CAAAtG,IAAA,CAAA2D,QAAA;UACA,IAAAnC,QAAA,GAAA8E,MAAA,CAAAtG,IAAA,CAAAwB,QAAA;UAEA,IAAAA,QAAA,IAAApC,SAAA;YACA,IAAAqH,oBAAA,EAAAH,MAAA,CAAAtG,IAAA,EAAAwC,IAAA,WAAAC,QAAA;cACA6D,MAAA,CAAAN,MAAA,CAAAU,UAAA;cACAJ,MAAA,CAAAvH,IAAA;;cAEA;cACA,KAAAuH,MAAA,CAAAzF,YAAA,CAAA8F,QAAA,CAAAnF,QAAA,KAAAA,QAAA;gBACA8E,MAAA,CAAAzF,YAAA,CAAA+F,IAAA,CAAApF,QAAA;cACA;;cAEA;cACA,IAAAmC,QAAA,KAAA2C,MAAA,CAAAzF,YAAA,CAAA8F,QAAA,CAAAhD,QAAA,KAAAA,QAAA;gBACA2C,MAAA,CAAAzF,YAAA,CAAA+F,IAAA,CAAAjD,QAAA;cACA;cAEA2C,MAAA,CAAAvE,OAAA;;cAEA;cACA,IAAAuE,MAAA,CAAA3F,aAAA,CAAAa,QAAA,KAAAA,QAAA;gBACA8E,MAAA,CAAAO,mBAAA,CAAArF,QAAA;cACA;cAEA8E,MAAA,CAAA7H,WAAA;YACA,GAAAmH,KAAA;cACAU,MAAA,CAAA7H,WAAA;YACA;UACA;YACA,IAAAqI,iBAAA,EAAAR,MAAA,CAAAtG,IAAA,EAAAwC,IAAA,WAAAC,QAAA;cACA6D,MAAA,CAAAN,MAAA,CAAAU,UAAA;cACAJ,MAAA,CAAAvH,IAAA;;cAEA;cACA,IAAA4E,QAAA,KAAA2C,MAAA,CAAAzF,YAAA,CAAA8F,QAAA,CAAAhD,QAAA,KAAAA,QAAA;gBACA2C,MAAA,CAAAzF,YAAA,CAAA+F,IAAA,CAAAjD,QAAA;cACA;;cAEA;cACA,IAAAlB,QAAA,CAAAlE,IAAA,IAAAkE,QAAA,CAAAlE,IAAA,CAAAiD,QAAA,IAAAmC,QAAA;gBACA,IAAAoD,WAAA,GAAAtE,QAAA,CAAAlE,IAAA,CAAAiD,QAAA;gBACA8E,MAAA,CAAAzF,YAAA,CAAA+F,IAAA,CAAAG,WAAA;cACA;cAEAT,MAAA,CAAAvE,OAAA;cACAuE,MAAA,CAAA7H,WAAA;YACA,GAAAmH,KAAA;cACAU,MAAA,CAAA7H,WAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAuI,YAAA,WAAAA,aAAA9B,GAAA;MAAA,IAAA+B,MAAA;MACA,KAAA/B,GAAA,KAAAA,GAAA,CAAA1D,QAAA;QACA,KAAAwE,MAAA,CAAAC,QAAA;QACA;MACA;;MAEA;MACA,IAAAf,GAAA,CAAAzE,QAAA,IAAAyE,GAAA,CAAAzE,QAAA,CAAA0C,MAAA;QACA,KAAA6C,MAAA,CAAAC,QAAA;QACA;MACA;MAEA,KAAAD,MAAA,CAAAkB,OAAA,cAAAhC,GAAA,CAAA/F,UAAA,kBAAAqD,IAAA;QACA,WAAA2E,iBAAA,EAAAjC,GAAA,CAAA1D,QAAA;MACA,GAAAgB,IAAA;QACA;QACA,IAAA4E,eAAA,GAAAlC,GAAA,CAAA1D,QAAA;;QAEA;QACAyF,MAAA,CAAAlF,OAAA;;QAEA;QACA,IAAAkF,MAAA,CAAAtG,aAAA,IAAAsG,MAAA,CAAAtG,aAAA,CAAAa,QAAA,KAAA4F,eAAA;UACAH,MAAA,CAAAtG,aAAA;QACA;QAEAsG,MAAA,CAAAjB,MAAA,CAAAU,UAAA,SAAAxB,GAAA,CAAA/F,UAAA;MACA,GAAAyG,KAAA;IACA;IACA;IACAiB,mBAAA,WAAAA,oBAAArF,QAAA;MAAA,IAAA6F,MAAA;MACA,KAAA7F,QAAA;;MAEA;MACA,IAAA0E,iBAAA,EAAA1E,QAAA,EAAAgB,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAlE,IAAA;UACA;UACA8I,MAAA,CAAA1G,aAAA,GAAA8B,QAAA,CAAAlE,IAAA;QACA;MACA,GAAAqH,KAAA,WAAAC,KAAA;QACA3I,OAAA,CAAA2I,KAAA,aAAAA,KAAA;MACA;IACA;IACA;IACAyB,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAvI,WAAA,SAAAA,WAAA;MACA,KAAA2D,SAAA;QACA,IAAA4E,MAAA,CAAAvI,WAAA;UACAuI,MAAA,CAAA5F,KAAA,CAAAC,IAAA,CAAA4F,SAAA;QACA;UACAD,MAAA,CAAA5F,KAAA,CAAAC,IAAA,CAAA6F,WAAA;QACA;;QAEA;QACAF,MAAA,CAAApF,gBAAA;;QAEA;QACAoF,MAAA,CAAAG,QAAA;UACAvH,OAAA,EAAAoH,MAAA,CAAAvI,WAAA;UACAzB,IAAA;UACAoK,QAAA;QACA;MACA;IACA;IACA,YACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,OAAA;MACA3K,OAAA,CAAAC,GAAA;MACA;MACA,KAAA6I,MAAA,CAAAxH,OAAA;;MAEA;MACA,IAAA+D,kBAAA,OAAArD,WAAA,EAAAsD,IAAA,WAAAC,QAAA;QACA;QACAoF,OAAA,CAAAnJ,UAAA,GAAAmJ,OAAA,CAAAnF,UAAA,CAAAD,QAAA,CAAAlE,IAAA;QACAsJ,OAAA,CAAArJ,OAAA;;QAEA;QACAqJ,OAAA,CAAA7B,MAAA,CAAA8B,YAAA;QACAD,OAAA,CAAA7B,MAAA,CAAAU,UAAA;;QAEA;QACAqB,UAAA;UACA,IAAAF,OAAA,CAAAlG,KAAA,CAAAC,IAAA;YACA;YACAiG,OAAA,CAAAlG,KAAA,CAAAC,IAAA,CAAAoG,KAAA,CAAAC,gBAAA,GAAAJ,OAAA,CAAA7I,WAAA;;YAEA;YACA,IAAA6I,OAAA,CAAAlH,aAAA,IAAAkH,OAAA,CAAAlH,aAAA,CAAAa,QAAA;cACAqG,OAAA,CAAAhB,mBAAA,CAAAgB,OAAA,CAAAlH,aAAA,CAAAa,QAAA;YACA;UACA;QACA;MACA,GAAAoE,KAAA,WAAAC,KAAA;QACA3I,OAAA,CAAA2I,KAAA,eAAAA,KAAA;QACAgC,OAAA,CAAA7B,MAAA,CAAA8B,YAAA;QACAD,OAAA,CAAA7B,MAAA,CAAAC,QAAA;MACA;IACA;IACA;IACA9D,gBAAA,WAAAA,iBAAA;MACA,SAAAR,KAAA,CAAAC,IAAA;QACA,IAAAf,YAAA;;QAEA;QACA,IAAAqH,KAAA,QAAAvG,KAAA,CAAAC,IAAA,CAAAoG,KAAA,CAAAG,QAAA;QACA,SAAAC,GAAA,IAAAF,KAAA;UACA,IAAAA,KAAA,CAAAG,cAAA,CAAAD,GAAA,KAAAF,KAAA,CAAAE,GAAA,EAAAE,QAAA;YACAzH,YAAA,CAAA+F,IAAA,CAAA2B,MAAA,CAAAH,GAAA;UACA;QACA;QAEA,KAAAvH,YAAA,GAAAA,YAAA;QACA3D,OAAA,CAAAC,GAAA,mBAAA0D,YAAA;MACA;IACA;IACA;IACA+B,mBAAA,WAAAA,oBAAA;MAAA,IAAA4F,OAAA;MACA,SAAA7G,KAAA,CAAAC,IAAA;QACA;QACA,SAAA5C,WAAA;UACA,KAAA2C,KAAA,CAAAC,IAAA,CAAA4F,SAAA;UACA;QACA;;QAEA;QACA,SAAA3G,YAAA,SAAAA,YAAA,CAAAsC,MAAA;UACAjG,OAAA,CAAAC,GAAA,mBAAA0D,YAAA;UACA,IAAAqH,KAAA,QAAAvG,KAAA,CAAAC,IAAA,CAAAoG,KAAA,CAAAG,QAAA;;UAEA;UACA,KAAAtH,YAAA,CAAA4H,OAAA,WAAAL,GAAA;YACA,IAAAF,KAAA,CAAAE,GAAA;cACAF,KAAA,CAAAE,GAAA,EAAAE,QAAA;YACA;UACA;;UAEA;UACA,KAAA3G,KAAA,CAAAC,IAAA,CAAAoG,KAAA,CAAAU,YAAA,GAAAD,OAAA,WAAAvF,IAAA;YACAsF,OAAA,CAAA7G,KAAA,CAAAC,IAAA,CAAAoG,KAAA,CAAAW,gBAAA,CAAAzF,IAAA;UACA;QACA;MACA;IACA;IACA;IACA0F,YAAA,WAAAA,aAAA;MACA,KAAA9H,UAAA;MACA,KAAA+H,WAAA;IACA;IACA;IACAA,WAAA,WAAAA,YAAA;MACA,KAAA5H,QAAA;MACA,KAAAC,aAAA;MACA,KAAAC,eAAA;MACA,KAAAC,sBAAA;MACA,KAAAL,YAAA;MACA,KAAAC,aAAA;MACA,KAAAW,KAAA,CAAAmH,MAAA,SAAAnH,KAAA,CAAAmH,MAAA,CAAAC,UAAA;IACA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAhD,MAAA,CAAAiD,UAAA;IACA;IACA;IACAC,YAAA,WAAAA,aAAAC,IAAA;MACA,IAAAC,MAAA,GAAAD,IAAA,CAAA5L,IAAA,oBAAA4L,IAAA,CAAAhL,IAAA,CAAAoF,WAAA,GAAA8F,QAAA;MACA,IAAAC,OAAA,GAAAH,IAAA,CAAAI,IAAA;MAEA,KAAAH,MAAA;QACA,KAAApD,MAAA,CAAAC,QAAA;QACA;MACA;MACA,KAAAqD,OAAA;QACA,KAAAtD,MAAA,CAAAC,QAAA;QACA;MACA;MACA;IACA;IACA;IACAuD,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,KAAA,QAAA/H,KAAA,CAAAmH,MAAA,CAAAa,WAAA;MACA,KAAAD,KAAA,IAAAA,KAAA,CAAAvG,MAAA;QACA,KAAA6C,MAAA,CAAAiD,UAAA;QACA;MACA;MAEA,KAAAlI,YAAA;MACA,IAAAoI,IAAA,GAAAO,KAAA,IAAAE,GAAA;MAEA,IAAAC,qBAAA,EAAAV,IAAA,EAAA3G,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAqH,IAAA;UACAL,OAAA,CAAAvI,aAAA,GAAAuB,QAAA,CAAAlE,IAAA;UACAkL,OAAA,CAAAzD,MAAA,CAAAU,UAAA,kBAAA+C,OAAA,CAAAvI,aAAA,CAAAiC,MAAA;QACA;UACAsG,OAAA,CAAAzD,MAAA,CAAAC,QAAA,CAAAxD,QAAA,CAAAsH,GAAA;QACA;MACA,GAAAnE,KAAA,WAAAC,KAAA;QACA3I,OAAA,CAAA2I,KAAA,YAAAA,KAAA;QACA4D,OAAA,CAAAzD,MAAA,CAAAC,QAAA;MACA,GAAA+D,OAAA;QACAP,OAAA,CAAA1I,YAAA;MACA;IACA;IACA;IACAkJ,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA/I,eAAA,GAAA+I,SAAA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAAjF,GAAA,EAAAkF,KAAA;MACA;IACA;IACA;IACAC,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,OAAA;MACA,UAAApJ,aAAA,SAAAA,aAAA,CAAAiC,MAAA;QACA,KAAA6C,MAAA,CAAAiD,UAAA;QACA;MACA;;MAEA;MACA,IAAA9H,eAAA,QAAAA,eAAA,SAAAA,eAAA,CAAAgC,MAAA,OACA,KAAAhC,eAAA,QAAAD,aAAA;MAEA,KAAAC,eAAA,IAAAA,eAAA,CAAAgC,MAAA;QACA,KAAA6C,MAAA,CAAAiD,UAAA;QACA;MACA;MAEA,KAAAjD,MAAA,CAAAkB,OAAA,eAAA/F,eAAA,CAAAgC,MAAA,aAAAX,IAAA;QACA8H,OAAA,CAAAtJ,aAAA;;QAEA;QACA,IAAAuJ,YAAA;UACAC,OAAA,EAAArJ,eAAA;UACAsJ,cAAA,EAAAH,OAAA,CAAAlJ;QACA;QAEA,IAAAsJ,0BAAA,EAAAH,YAAA,EAAA/H,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAqH,IAAA;YACAQ,OAAA,CAAAtE,MAAA,CAAAU,UAAA,CAAAjE,QAAA,CAAAsH,GAAA;YACAO,OAAA,CAAAxJ,UAAA;YACAwJ,OAAA,CAAAvI,OAAA;UACA;YACAuI,OAAA,CAAAtE,MAAA,CAAAC,QAAA,CAAAxD,QAAA,CAAAsH,GAAA;UACA;QACA,GAAAnE,KAAA,WAAAC,KAAA;UACA3I,OAAA,CAAA2I,KAAA,YAAAA,KAAA;UACAyE,OAAA,CAAAtE,MAAA,CAAAC,QAAA;QACA,GAAA+D,OAAA;UACAM,OAAA,CAAAtJ,aAAA;QACA;MACA;IACA;IACA;IACA2J,YAAA,WAAAA,aAAA;MACA,KAAA7J,UAAA;MACA,KAAA+H,WAAA;IACA;EACA;AACA", "ignoreList": []}]}