package com.zhike.system.controller;

import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.zhike.common.annotation.Log;
import com.zhike.common.constant.UserConstants;
import com.zhike.common.core.controller.BaseController;
import com.zhike.common.core.domain.AjaxResult;
import com.zhike.common.enums.BusinessType;
import com.zhike.common.utils.StringUtils;
import com.zhike.system.domain.SysModule;
import com.zhike.system.service.ISysModuleService;

/**
 * 系统模块信息
 */
@RestController
@RequestMapping("/system/module")
public class SysModuleController extends BaseController {
    @Autowired
    private ISysModuleService moduleService;

    /**
     * 获取系统模块列表
     */
    @PreAuthorize("@ss.hasPermi('system:module:list')")
    @GetMapping("/list")
    public AjaxResult list(SysModule module) {
        List<SysModule> modules = moduleService.selectModuleList(module);
        return success(modules);
    }

    /**
     * 根据系统模块编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:module:query')")
    @GetMapping(value = "/{moduleId}")
    public AjaxResult getInfo(@PathVariable Long moduleId) {
        return success(moduleService.selectModuleById(moduleId));
    }

    /**
     * 新增系统模块
     */
    @PreAuthorize("@ss.hasPermi('system:module:add')")
    @Log(title = "系统模块管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysModule module) {
        if (moduleNameUnique(module)) {
            return error("新增系统模块'" + module.getModuleName() + "'失败，模块名称已存在");
        }
        if (moduleCodeUnique(module)) {
            return error("新增系统模块'" + module.getModuleCode() + "'失败，模块编码已存在");
        }
        module.setCreateBy(getUsername());
        return toAjax(moduleService.insertModule(module));
    }

    /**
     * 修改系统模块
     */
    @PreAuthorize("@ss.hasPermi('system:module:edit')")
    @Log(title = "系统模块管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysModule module) {
        if (moduleNameUnique(module)) {
            return error("修改系统模块'" + module.getModuleName() + "'失败，模块名称已存在");
        }
        if (moduleCodeUnique(module)) {
            return error("修改系统模块'" + module.getModuleCode() + "'失败，模块编码已存在");
        }
        if (module.getParentId().equals(module.getModuleId())) {
            return error("修改系统模块'" + module.getModuleName() + "'失败，上级模块不能是自己");
        }
        module.setUpdateBy(getUsername());
        return toAjax(moduleService.updateModule(module));
    }

    /**
     * 删除系统模块
     */
    @PreAuthorize("@ss.hasPermi('system:module:remove')")
    @Log(title = "系统模块管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{moduleId}")
    public AjaxResult remove(@PathVariable Long moduleId) {
        if (moduleService.hasChildByModuleId(moduleId) > 0) {
            return warn("存在下级模块,不允许删除");
        }
        return toAjax(moduleService.deleteModuleById(moduleId));
    }

    /**
     * 解析HTML文件中的模块数据
     */
    @PreAuthorize("@ss.hasPermi('system:module:add')")
    @PostMapping("/parse-html")
    public AjaxResult parseHtml(@RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return error("上传文件不能为空");
            }

            String fileName = file.getOriginalFilename();
            if (!fileName.toLowerCase().endsWith(".html")) {
                return error("只支持HTML文件格式");
            }

            List<Map<String, Object>> moduleData = moduleService.parseHtmlFile(file);
            return success(moduleData);
        } catch (Exception e) {
            logger.error("解析HTML文件失败", e);
            return error("解析HTML文件失败：" + e.getMessage());
        }
    }

    /**
     * 批量导入模块
     */
    @PreAuthorize("@ss.hasPermi('system:module:add')")
    @Log(title = "系统模块管理", businessType = BusinessType.IMPORT)
    @PostMapping("/batch-import")
    public AjaxResult batchImport(@RequestBody Map<String, Object> importRequest) {
        try {
            // 获取模块数据
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> moduleData = (List<Map<String, Object>>) importRequest.get("modules");

            if (moduleData == null || moduleData.isEmpty()) {
                return error("导入数据不能为空");
            }

            // 获取父模块ID
            Long parentModuleId = null;
            Object parentIdObj = importRequest.get("parentModuleId");
            if (parentIdObj != null) {
                if (parentIdObj instanceof Number) {
                    parentModuleId = ((Number) parentIdObj).longValue();
                } else if (parentIdObj instanceof String && !((String) parentIdObj).isEmpty()) {
                    try {
                        parentModuleId = Long.parseLong((String) parentIdObj);
                    } catch (NumberFormatException e) {
                        return error("父模块ID格式错误");
                    }
                }
            }

            int result = moduleService.batchImportModules(moduleData, parentModuleId, getUsername());
            return success("成功导入 " + result + " 个模块");
        } catch (Exception e) {
            logger.error("批量导入模块失败", e);
            return error("批量导入模块失败：" + e.getMessage());
        }
    }

    /**
     * 校验系统模块名称是否唯一
     */
    private boolean moduleNameUnique(SysModule module) {
        Long moduleId = StringUtils.isNull(module.getModuleId()) ? -1L : module.getModuleId();
        SysModule info = moduleService.selectModuleById(module.getParentId());
        if (StringUtils.isNotNull(info) && info.getModuleId().longValue() == moduleId.longValue()) {
            return true;
        }
        return false;
    }

    /**
     * 校验系统模块编码是否唯一
     */
    private boolean moduleCodeUnique(SysModule module) {
        Long moduleId = StringUtils.isNull(module.getModuleId()) ? -1L : module.getModuleId();
        SysModule info = moduleService.selectModuleById(module.getParentId());
        if (StringUtils.isNotNull(info) && info.getModuleId().longValue() == moduleId.longValue()) {
            return true;
        }
        return false;
    }
}