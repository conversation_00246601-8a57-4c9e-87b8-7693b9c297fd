{"remainingRequest": "D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ruoyi\\Zhike\\zhike-ui\\src\\views\\system\\module\\index.vue?vue&type=style&index=0&id=310f19a5&scoped=true&lang=css", "dependencies": [{"path": "D:\\ruoyi\\Zhike\\zhike-ui\\src\\views\\system\\module\\index.vue", "mtime": 1754215759744}, {"path": "D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1737340299655}, {"path": "D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1737340302641}, {"path": "D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1737340300968}, {"path": "D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737340298796}, {"path": "D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737340301724}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLm1iOCB7CiAgbWFyZ2luLWJvdHRvbTogOHB4Owp9CgoudHJlZS1jb250YWluZXIgewogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIGJveC1zaGFkb3c6IDAgMnB4IDEycHggMCByZ2JhKDAsIDAsIDAsIDAuMSk7CiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gMTMwcHgpOwogIG92ZXJmbG93OiBhdXRvOwp9CgoudHJlZS1oZWFkZXIgewogIHBhZGRpbmc6IDEwcHg7CiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlYmVlZjU7Cn0KCi50cmVlLWFjdGlvbnMgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIG1hcmdpbi10b3A6IDhweDsKfQoKLm1vZHVsZS1kZXRhaWwgewogIG1hcmdpbi10b3A6IDEwcHg7Cn0KCi5lbXB0eS10aXAgewogIHBhZGRpbmc6IDQwcHggMDsKICB0ZXh0LWFsaWduOiBjZW50ZXI7Cn0KCi5jdXN0b20tdHJlZS1ub2RlIHsKICBmbGV4OiAxOwogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgZm9udC1zaXplOiAxNHB4OwogIHBhZGRpbmctcmlnaHQ6IDhweDsKfQoKLmNhcmQtdGl0bGUgewogIGZvbnQtc2l6ZTogMTVweDsKICBmb250LXdlaWdodDogYm9sZDsKfQoKLmltcG9ydC1oZWFkZXIgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgbWFyZ2luLWJvdHRvbTogMTBweDsKICBwYWRkaW5nOiAxMHB4Owp9CgoucGFyZW50LW1vZHVsZS1zZWxlY3RvciB7CiAgbWFyZ2luLWJvdHRvbTogMTVweDsKICBwYWRkaW5nOiAxMHB4OwogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7CiAgYm9yZGVyLXJhZGl1czogNHB4Owp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2sCA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/module", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索区域 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" class=\"mb8\">\n      <el-form-item label=\"模块名称\" prop=\"moduleName\">\n        <el-input\n          v-model=\"queryParams.moduleName\"\n          placeholder=\"请输入模块名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"模块编码\" prop=\"moduleCode\">\n        <el-input\n          v-model=\"queryParams.moduleCode\"\n          placeholder=\"请输入模块编码\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"业务类型\" prop=\"businessType\">\n        <el-select v-model=\"queryParams.businessType\" placeholder=\"请选择业务类型\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.product_plan_type\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"所属厂商\" prop=\"competitorId\">\n        <el-select v-model=\"queryParams.competitorId\" placeholder=\"请选择厂商\" clearable filterable>\n          <el-option\n            v-for=\"item in competitorOptions\"\n            :key=\"item.id\"\n            :label=\"item.name\"\n            :value=\"item.id\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"模块类型\" prop=\"moduleType\">\n        <el-select v-model=\"queryParams.moduleType\" placeholder=\"模块类型\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_module_type\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"模块状态\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_normal_disable\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"模块性质\" prop=\"nature\">\n        <el-select v-model=\"queryParams.nature\" placeholder=\"请选择模块性质\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_module_nature\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"报价类型\" prop=\"quotationNature\">\n        <el-select v-model=\"queryParams.quotationNature\" placeholder=\"请选择报价类型\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_quotation_nature\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"是否BD\" prop=\"isBd\">\n        <el-select v-model=\"queryParams.isBd\" placeholder=\"请选择是否BD\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_yes_no\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"医院评级\" prop=\"hospitalCertification\">\n        <el-select v-model=\"queryParams.hospitalCertification\" placeholder=\"请选择医院评级\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.hospital_certification\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n\n      <el-form-item label=\"价格范围\" prop=\"priceRange\">\n        <el-input-number\n          v-model=\"queryParams.minPrice\"\n          placeholder=\"最小价格\"\n          :min=\"0\"\n          :precision=\"2\"\n          style=\"width: 120px\"\n        />\n        <span style=\"margin: 0 10px\">-</span>\n        <el-input-number\n          v-model=\"queryParams.maxPrice\"\n          placeholder=\"最大价格\"\n          :min=\"0\"\n          :precision=\"2\"\n          style=\"width: 120px\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"20\">\n      <!-- 左侧树形结构 -->\n      <el-col :span=\"6\" class=\"tree-container\">\n        <div class=\"tree-header\">\n          <el-input\n            v-model=\"filterText\"\n            placeholder=\"请输入关键字进行过滤\"\n            clearable\n            prefix-icon=\"el-icon-search\"\n            size=\"small\"\n          />\n          <div class=\"tree-actions\">\n            <el-button\n              type=\"text\"\n              icon=\"el-icon-sort\"\n              size=\"mini\"\n              @click=\"toggleExpandAll\"\n            >{{ isExpandAll ? '全部折叠' : '全部展开' }}</el-button>\n            <el-button\n              type=\"text\"\n              icon=\"el-icon-refresh\"\n              size=\"mini\"\n              @click=\"refreshTree\"\n            >刷新树</el-button>\n            <el-button\n              type=\"text\"\n              icon=\"el-icon-search\"\n              size=\"mini\"\n              @click=\"showSearch = !showSearch\"\n            >{{ showSearch ? '收起搜索' : '高级搜索' }}</el-button>\n          </div>\n        </div>\n        <el-tree\n          ref=\"tree\"\n          :data=\"moduleList\"\n          :props=\"defaultProps\"\n          :filter-node-method=\"filterNode\"\n          :expand-on-click-node=\"false\"\n          node-key=\"moduleId\"\n          :default-expand-all=\"isExpandAll\"\n          highlight-current\n          @node-click=\"handleNodeClick\"\n        >\n          <span class=\"custom-tree-node\" slot-scope=\"{ node, data }\">\n            <span>\n              <i class=\"el-icon-folder\" style=\"margin-right: 5px\"></i>\n              {{ node.label }}\n            </span>\n          </span>\n        </el-tree>\n      </el-col>\n\n      <!-- 右侧内容区域 -->\n      <el-col :span=\"18\">\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span class=\"card-title\">{{ currentModule.moduleName || '请选择模块' }}</span>\n            <el-button-group style=\"float: right\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['system:module:add']\"\n        >新增</el-button>\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-upload2\"\n          size=\"mini\"\n          @click=\"handleImport\"\n          v-hasPermi=\"['system:module:add']\"\n        >导入模块</el-button>\n              <el-button\n                type=\"success\"\n                plain\n                icon=\"el-icon-edit\"\n                size=\"mini\"\n                @click=\"handleUpdate(currentModule)\"\n                v-hasPermi=\"['system:module:edit']\"\n                v-if=\"currentModule.moduleId\"\n              >修改</el-button>\n              <el-button\n                type=\"danger\"\n                plain\n                icon=\"el-icon-delete\"\n                size=\"mini\"\n                @click=\"handleDelete(currentModule)\"\n                v-hasPermi=\"['system:module:remove']\"\n                v-if=\"currentModule.moduleId\"\n              >删除</el-button>\n        <el-button\n          type=\"info\"\n          plain\n                icon=\"el-icon-plus\"\n          size=\"mini\"\n                @click=\"handleAdd(currentModule)\"\n                v-hasPermi=\"['system:module:add']\"\n                v-if=\"currentModule.moduleId\"\n              >添加子模块</el-button>\n            </el-button-group>\n          </div>\n\n          <!-- 模块详情内容 -->\n          <div v-if=\"currentModule.moduleId\" class=\"module-detail\">\n            <el-descriptions :column=\"2\" border>\n              <el-descriptions-item label=\"模块名称\">{{ currentModule.moduleName }}</el-descriptions-item>\n              <el-descriptions-item label=\"模块编码\">{{ currentModule.moduleCode }}</el-descriptions-item>\n              <el-descriptions-item label=\"显示顺序\">{{ currentModule.orderNum }}</el-descriptions-item>\n              <el-descriptions-item label=\"上级模块\">{{ getParentModuleName(currentModule.parentId) }}</el-descriptions-item>\n              <el-descriptions-item label=\"模块类型\">\n                <dict-tag :options=\"dict.type.sys_module_type\" :value=\"currentModule.moduleType\"/>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"模块属性\">\n                <dict-tag :options=\"dict.type.sys_module_property\" :value=\"currentModule.moduleProperty\"/>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"所属厂商\">{{ currentModule.competitorName }}</el-descriptions-item>\n              <el-descriptions-item label=\"模块状态\">\n                <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"currentModule.status\"/>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"性质\">\n                <dict-tag :options=\"dict.type.sys_module_nature\" :value=\"currentModule.nature\"/>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"报价性质\">\n                <dict-tag :options=\"dict.type.sys_quotation_nature\" :value=\"currentModule.quotationNature\"/>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"是否BD\">\n                <dict-tag :options=\"dict.type.sys_yes_no\" :value=\"currentModule.isBd\"/>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"医院评级\">\n                <dict-tag :options=\"dict.type.hospital_certification\" :value=\"currentModule.hospitalCertification\"/>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"模块说明\">{{ currentModule.moduleDesc || '-' }}</el-descriptions-item>\n              <el-descriptions-item label=\"业务类型\">\n                <dict-tag :options=\"dict.type.product_plan_type\" :value=\"currentModule.businessType\"/>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"价格\">\n                <span v-if=\"currentModule.price\">¥{{ currentModule.price }}</span>\n                <span v-else>-</span>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"创建时间\">{{ parseTime(currentModule.createTime) }}</el-descriptions-item>\n              <el-descriptions-item label=\"更新时间\">{{ parseTime(currentModule.updateTime) }}</el-descriptions-item>\n              <el-descriptions-item label=\"备注\" :span=\"2\">{{ currentModule.remark }}</el-descriptions-item>\n            </el-descriptions>\n          </div>\n          <div v-else class=\"empty-tip\">\n            <el-empty description=\"请从左侧选择模块\"></el-empty>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 添加或修改系统模块对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"上级模块\" prop=\"parentId\">\n              <treeselect \n                v-model=\"form.parentId\" \n                :options=\"moduleOptions\" \n                :normalizer=\"normalizer\" \n                placeholder=\"选择上级模块\"\n                :disabled=\"form.parentId === 0\" \n                :clearable=\"false\"\n                :default-expand-level=\"1\"\n                :show-count=\"true\"\n                noOptionsMessage=\"无\"\n                noResultsText=\"无\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"模块名称\" prop=\"moduleName\">\n              <el-input v-model=\"form.moduleName\" placeholder=\"请输入模块名称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"模块编码\" prop=\"moduleCode\">\n              <el-input v-model=\"form.moduleCode\" placeholder=\"请输入模块编码\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"显示排序\" prop=\"orderNum\">\n              <el-input-number v-model=\"form.orderNum\" controls-position=\"right\" :min=\"0\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"模块类型\" prop=\"moduleType\">\n              <el-select v-model=\"form.moduleType\" placeholder=\"请选择\">\n                <el-option\n                  v-for=\"dict in dict.type.sys_module_type\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"模块属性\" prop=\"moduleProperty\">\n              <el-select v-model=\"form.moduleProperty\" placeholder=\"请选择\">\n                <el-option\n                  v-for=\"dict in dict.type.sys_module_property\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"所属厂商\" prop=\"competitorId\">\n              <el-select\n                v-model=\"form.competitorId\"\n                placeholder=\"请选择厂商\"\n                filterable\n                clearable\n              >\n                <el-option\n                  v-for=\"item in competitorOptions\"\n                  :key=\"item.id\"\n                  :label=\"item.name\"\n                  :value=\"item.id\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"模块状态\">\n              <el-radio-group v-model=\"form.status\">\n                <el-radio\n                  v-for=\"dict in dict.type.sys_normal_disable\"\n                  :key=\"dict.value\"\n                  :label=\"dict.value\"\n                >{{dict.label}}</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"性质\" prop=\"nature\">\n              <el-select v-model=\"form.nature\" placeholder=\"请选择\">\n                <el-option\n                  v-for=\"dict in dict.type.sys_module_nature\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"报价性质\" prop=\"quotationNature\">\n              <el-select v-model=\"form.quotationNature\" placeholder=\"请选择\">\n                <el-option\n                  v-for=\"dict in dict.type.sys_quotation_nature\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否BD\" prop=\"isBd\">\n              <el-select v-model=\"form.isBd\" placeholder=\"请选择\">\n                <el-option\n                  v-for=\"dict in dict.type.sys_yes_no\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n                     <el-col :span=\"12\">\n             <el-form-item label=\"医院评级\" prop=\"hospitalCertification\">\n               <el-select v-model=\"form.hospitalCertification\" placeholder=\"请选择\">\n                 <el-option\n                   v-for=\"dict in dict.type.hospital_certification\"\n                   :key=\"dict.value\"\n                   :label=\"dict.label\"\n                   :value=\"dict.value\"\n                 ></el-option>\n               </el-select>\n             </el-form-item>\n           </el-col>\n           <el-col :span=\"12\">\n             <el-form-item label=\"价格\" prop=\"price\">\n               <el-input-number\n                 v-model=\"form.price\"\n                 placeholder=\"请输入价格\"\n                 :min=\"0\"\n                 :precision=\"2\"\n                 controls-position=\"right\"\n                 style=\"width: 100%\"\n               />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"业务类型\" prop=\"businessType\">\n              <el-select v-model=\"form.businessType\" placeholder=\"请选择业务类型\">\n                <el-option\n                  v-for=\"dict in dict.type.product_plan_type\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"模块说明\" prop=\"moduleDesc\">\n              <el-input v-model=\"form.moduleDesc\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入模块说明\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"remark\">\n              <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入备注\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\" :loading=\"formLoading\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 模块导入对话框 -->\n    <el-dialog title=\"模块导入\" :visible.sync=\"importOpen\" width=\"800px\" append-to-body>\n      <div v-if=\"!parsedModules.length\">\n        <el-upload\n          ref=\"upload\"\n          action=\"#\"\n          :limit=\"1\"\n          accept=\".html\"\n          :on-exceed=\"handleExceed\"\n          :before-upload=\"beforeUpload\"\n          :auto-upload=\"false\"\n          :file-list=\"fileList\"\n          drag\n        >\n          <i class=\"el-icon-upload\"></i>\n          <div class=\"el-upload__text\">将HTML文件拖到此处，或<em>点击上传</em></div>\n          <div class=\"el-upload__tip\" slot=\"tip\">只能上传HTML文件，且不超过10MB</div>\n        </el-upload>\n        <div style=\"margin-top: 20px; text-align: center\">\n          <el-button type=\"primary\" @click=\"handleParseFile\" :loading=\"parseLoading\">解析文件</el-button>\n        </div>\n      </div>\n\n      <div v-else>\n        <div class=\"import-header\">\n          <span>解析结果：共找到 {{ parsedModules.length }} 个模块</span>\n          <el-button type=\"text\" @click=\"resetImport\">重新选择文件</el-button>\n        </div>\n\n        <!-- 父模块选择 -->\n        <div class=\"parent-module-selector\">\n          <el-form :inline=\"true\">\n            <el-form-item label=\"导入到父模块：\">\n              <treeselect\n                v-model=\"selectedParentModuleId\"\n                :options=\"moduleOptions\"\n                :normalizer=\"normalizer\"\n                :show-count=\"true\"\n                placeholder=\"选择父模块（可选）\"\n                :clearable=\"true\"\n                style=\"width: 300px\"\n              />\n            </el-form-item>\n          </el-form>\n        </div>\n\n        <el-table\n          ref=\"importTable\"\n          :data=\"parsedModules\"\n          border\n          style=\"width: 100%; margin-top: 10px\"\n          max-height=\"400\"\n          @selection-change=\"handleSelectionChange\"\n        >\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" :selectable=\"checkSelectable\" />\n          <el-table-column prop=\"moduleName\" label=\"模块名称\" min-width=\"200\">\n            <template slot-scope=\"scope\">\n              <span\n                :style=\"{\n                  'margin-left': (scope.row.level - 1) * 20 + 'px',\n                  'font-weight': scope.row.isParent ? 'bold' : 'normal',\n                  'color': scope.row.isParent ? '#409EFF' : '#606266'\n                }\"\n              >\n                <i v-if=\"scope.row.isParent\" class=\"el-icon-folder\" style=\"margin-right: 5px\"></i>\n                <i v-else class=\"el-icon-document\" style=\"margin-right: 5px\"></i>\n                {{ scope.row.moduleName }}\n              </span>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"moduleCode\" label=\"模块编码\" width=\"150\" />\n          <el-table-column prop=\"parentName\" label=\"父模块\" width=\"150\">\n            <template slot-scope=\"scope\">\n              {{ scope.row.parentName || '无' }}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"level\" label=\"层级\" width=\"60\">\n            <template slot-scope=\"scope\">\n              <el-tag :type=\"scope.row.level === 1 ? 'primary' : scope.row.level === 2 ? 'success' : 'warning'\" size=\"mini\">\n                {{ scope.row.level || 1 }}级\n              </el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"orderNum\" label=\"排序\" width=\"80\" />\n        </el-table>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button v-if=\"parsedModules.length\" type=\"primary\" @click=\"handleConfirmImport\" :loading=\"importLoading\">\n          确认导入\n        </el-button>\n        <el-button @click=\"cancelImport\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listModule, getModule, delModule, addModule, updateModule, parseHtmlFile, batchImportModules } from \"@/api/system/module\";\nimport { listCompetitor } from \"@/api/competitor/competitor\";\nimport Treeselect from \"@riophae/vue-treeselect\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\nimport { pinyin } from 'pinyin-pro';\n\n// 添加汉字转拼音的工具函数\nfunction convertToPinyin(chinese) {\n  if (!chinese) return '';\n  console.log('输入的中文：', chinese);\n  \n  // 将汉字转换为拼音，并转换为大写\n  const pinyinResult = pinyin(chinese, {\n    toneType: 'none',    // 不带声调\n    type: 'array',       // 返回拼音数组\n    nonZh: 'consecutive', // 非汉字连续返回\n    pattern: 'first'     // 只取首字母\n  });\n  console.log('pinyin转换结果：', pinyinResult);\n  \n  const upperResult = pinyinResult.map(py => py.toUpperCase());\n  console.log('转大写结果：', upperResult);\n  \n  const finalResult = upperResult.join('');\n  console.log('最终结果：', finalResult);\n  \n  return finalResult;\n}\n\nexport default {\n  name: \"Module\",\n  dicts: ['sys_normal_disable', 'sys_module_type', 'sys_module_property', 'sys_module_nature', 'sys_quotation_nature', 'sys_yes_no', 'hospital_certification', 'product_plan_type'],\n  components: { Treeselect },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 表单加载状态\n      formLoading: false,\n      // 表格树数据\n      moduleList: [],\n      // 模块树选项\n      moduleOptions: [],\n      // 厂商选项\n      competitorOptions: [],\n      // 默认卫宁厂商ID\n      defaultCompetitorId: null,\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 是否展开，默认全部展开\n      isExpandAll: false,\n      // 重新渲染表格状态\n      refreshTable: true,\n      // 查询参数\n      queryParams: {\n        moduleName: undefined,\n        moduleCode: undefined,\n        moduleType: undefined,\n        status: undefined,\n        competitorId: undefined,\n        nature: undefined,\n        quotationNature: undefined,\n        isBd: undefined,\n        hospitalCertification: undefined,\n        minPrice: undefined,\n        maxPrice: undefined,\n        businessType: undefined\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        moduleName: [\n          { required: true, message: \"模块名称不能为空\", trigger: \"blur\" }\n        ],\n        orderNum: [\n          { required: true, message: \"显示排序不能为空\", trigger: \"blur\" }\n        ],\n        moduleType: [\n          { required: true, message: \"模块类型不能为空\", trigger: \"blur\" }\n        ],\n        moduleProperty: [\n          { required: true, message: \"模块属性不能为空\", trigger: \"blur\" }\n        ],\n        competitorId: [\n          { required: true, message: \"请选择所属厂商\", trigger: \"change\" }\n        ],\n        businessType: [\n          { required: true, message: \"请选择业务类型\", trigger: \"change\" }\n        ]\n      },\n      // 树形结构相关\n      filterText: '',\n      defaultProps: {\n        children: 'children',\n        label: 'moduleName'\n      },\n      currentModule: {}, // 当前选中的模块\n      showSearch: false, // 默认不显示搜索条件\n      expandedKeys: [], // 当前展开的节点ID列表\n      // 导入相关\n      importOpen: false, // 导入对话框是否显示\n      parseLoading: false, // 解析加载状态\n      importLoading: false, // 导入加载状态\n      fileList: [], // 上传文件列表\n      parsedModules: [], // 解析出的模块数据\n      selectedModules: [], // 选中的模块数据\n      selectedParentModuleId: null, // 选择的父模块ID\n    };\n  },\n  watch: {\n    // 监听模块名称变化，自动生成模块编码\n    'form.moduleName': {\n      handler(newVal) {\n        // 在新增或编辑时都自动生成编码\n        if (newVal) {\n          console.log('模块名称变化：', newVal);\n          this.form.moduleCode = convertToPinyin(newVal);\n          console.log('生成的模块编码：', this.form.moduleCode);\n        }\n      }\n    },\n    // 监听上级模块变化，重新获取显示排序\n    'form.parentId': {\n      handler(newVal) {\n        // 如果是新增模式（没有moduleId）并且选择了有效的上级模块\n        if (!this.form.moduleId && newVal !== undefined) {\n          this.getMaxOrderNum(newVal);\n        }\n      }\n    },\n    // 监听过滤文本变化\n    filterText(val) {\n      this.$refs.tree.filter(val);\n    }\n  },\n  created() {\n    this.getList();\n    this.getCompetitorOptions();\n  },\n  methods: {\n    /** 查询模块列表 */\n    getList() {\n      this.loading = true;\n      \n      // 保存当前展开的节点ID\n      this.saveExpandedKeys();\n      \n      // 确保将厂商ID传递给后端接口\n      let params = Object.assign({}, this.queryParams);\n      \n      listModule(params).then(response => {\n        this.moduleList = this.handleTree(response.data, \"moduleId\");\n        this.loading = false;\n        \n        // 恢复展开的节点\n        this.$nextTick(() => {\n          this.restoreExpandedKeys();\n        });\n      });\n    },\n    /** 查询厂商选项 */\n    getCompetitorOptions() {\n      listCompetitor({ status: '0', pageSize: 9999 }).then(response => {\n        this.competitorOptions = response.rows;\n      });\n    },\n    /** 转换模块数据结构 */\n    normalizer(node) {\n      if (node.children && !node.children.length) {\n        delete node.children;\n      }\n      return {\n        id: node.moduleId,\n        label: node.moduleName,\n        children: node.children\n      };\n    },\n    // 过滤节点\n    filterNode(value, data) {\n      if (!value) return true;\n      return data.moduleName.toLowerCase().indexOf(value.toLowerCase()) !== -1;\n    },\n    // 节点点击事件\n    handleNodeClick(data) {\n      this.currentModule = data;\n    },\n    // 获取父模块名称\n    getParentModuleName(parentId) {\n      if (!parentId || parentId === 0) {\n        return '无';\n      }\n      \n      // 递归在模块树中查找指定ID的模块\n      const findModule = (list, id) => {\n        for (const item of list) {\n          if (item.moduleId === id) {\n            return item.moduleName;\n          }\n          if (item.children && item.children.length > 0) {\n            const found = findModule(item.children, id);\n            if (found) return found;\n          }\n        }\n        return null;\n      };\n      \n      const name = findModule(this.moduleList, parentId);\n      return name || '未知';\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        moduleId: undefined,\n        parentId: undefined,\n        moduleName: undefined,\n        moduleCode: undefined,\n        orderNum: 99, // 设置默认排序为99\n        moduleType: \"1\",  // 修改默认值为系统模块\n        moduleProperty: \"3\", // 默认为功能\n        competitorId: undefined, // 取消默认厂商设置\n        status: \"0\",\n        nature: undefined,\n        quotationNature: undefined,\n        isBd: undefined,\n        hospitalCertification: undefined,\n        price: undefined,\n        remark: undefined,\n        moduleDesc: undefined,\n        businessType: undefined\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    /** 新增按钮操作 */\n    handleAdd(row) {\n      this.reset();\n      if (row && row.moduleId) {\n        this.form.parentId = row.moduleId;\n        // 如果是子模块，继承父模块的厂商ID\n        if (row.competitorId) {\n          this.form.competitorId = row.competitorId;\n        }\n        \n        // 获取同级模块的最大排序号\n        this.getMaxOrderNum(row.moduleId);\n      } else {\n        // 获取顶级模块的最大排序号\n        this.getMaxOrderNum(0);\n      }\n      this.open = true;\n      this.title = \"添加模块\";\n      listModule().then(response => {\n        this.moduleOptions = this.handleTree(response.data, \"moduleId\");\n      });\n    },\n    /** 获取同级模块中的最大排序号 */\n    getMaxOrderNum(parentId) {\n      // 明确查询同一父模块下的直接子模块\n      listModule({ parentId: parentId }).then(response => {\n        if (response.data && response.data.length > 0) {\n          // 过滤出具有相同parentId的模块，确保只处理真正的同级模块\n          const siblingModules = response.data.filter(item => item.parentId === parentId);\n          \n          if (siblingModules.length > 0) {\n            // 找出同级模块中的最大排序号\n            const maxOrderNum = Math.max(...siblingModules.map(m => m.orderNum || 0));\n            // 设置新模块的排序号为最大值+1\n            this.form.orderNum = maxOrderNum + 1;\n            console.log('获取到同级模块最大排序号：', maxOrderNum, '设置新模块排序号为：', this.form.orderNum);\n          } else {\n            // 如果该父模块下没有子模块，则设置排序号为1\n            this.form.orderNum = 1;\n            console.log('该父模块下无子模块，设置新模块排序号为：1');\n          }\n        } else {\n          // 如果没有返回数据，则设置排序号为1\n          this.form.orderNum = 1;\n          console.log('查询无返回数据，设置新模块排序号为：1');\n        }\n      }).catch(error => {\n        console.error('获取同级模块排序号失败：', error);\n        // 发生错误时设置默认排序号\n        this.form.orderNum = 1;\n      });\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      if (!row || !row.moduleId) {\n        this.$modal.msgError(\"请先选择要修改的模块\");\n        return;\n      }\n      \n      getModule(row.moduleId).then(response => {\n        this.form = response.data;\n        // 如果是顶级模块(parentId=0),则禁用上级模块选择\n        if (this.form.parentId === 0) {\n          this.$nextTick(() => {\n            this.$refs.form.clearValidate('parentId');\n            this.$set(this.rules, 'parentId', []);\n          });\n          // 添加一个顶级节点选项\n          this.moduleOptions = [{\n            id: 0,\n            label: '无'\n          }];\n        } else {\n          listModule().then(response => {\n            this.moduleOptions = this.handleTree(response.data, \"moduleId\");\n          });\n        }\n        this.open = true;\n        this.title = \"修改模块\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm: function() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          this.formLoading = true;\n          \n          // 保存当前展开的节点ID\n          this.saveExpandedKeys();\n          \n          // 记录当前表单的父节点ID和本身ID\n          const parentId = this.form.parentId;\n          const moduleId = this.form.moduleId;\n          \n          if (moduleId != undefined) {\n            updateModule(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              \n              // 将当前节点ID添加到展开节点列表\n              if (!this.expandedKeys.includes(moduleId) && moduleId !== 0) {\n                this.expandedKeys.push(moduleId);\n              }\n              \n              // 将父节点ID添加到展开节点列表\n              if (parentId && !this.expandedKeys.includes(parentId) && parentId !== 0) {\n                this.expandedKeys.push(parentId);\n              }\n              \n              this.getList();\n              \n              // 如果是当前模块，也要刷新详情\n              if (this.currentModule.moduleId === moduleId) {\n                this.refreshModuleDetail(moduleId);\n              }\n              \n              this.formLoading = false;\n            }).catch(() => {\n              this.formLoading = false;\n            });\n          } else {\n            addModule(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              \n              // 确保父节点在展开列表中\n              if (parentId && !this.expandedKeys.includes(parentId) && parentId !== 0) {\n                this.expandedKeys.push(parentId);\n              }\n              \n              // 如果是新增的根节点，记录其ID\n              if (response.data && response.data.moduleId && parentId === 0) {\n                const newModuleId = response.data.moduleId;\n                this.expandedKeys.push(newModuleId);\n              }\n              \n              this.getList();\n              this.formLoading = false;\n            }).catch(() => {\n              this.formLoading = false;\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      if (!row || !row.moduleId) {\n        this.$modal.msgError(\"未选择要删除的模块\");\n        return;\n      }\n      \n      // 检查是否有子节点\n      if (row.children && row.children.length > 0) {\n        this.$modal.msgError('该模块下存在子模块，请先删除子模块');\n        return;\n      }\n      \n      this.$modal.confirm('确定要删除模块【' + row.moduleName + '】吗？删除后无法恢复！').then(() => {\n        return delModule(row.moduleId);\n      }).then(() => {\n        // 记录当前删除的模块ID\n        const deletedModuleId = row.moduleId;\n        \n        // 刷新模块列表\n        this.getList();\n        \n        // 如果右侧当前显示的就是被删除的模块，则清空右侧内容\n        if (this.currentModule && this.currentModule.moduleId === deletedModuleId) {\n          this.currentModule = {};\n        }\n        \n        this.$modal.msgSuccess(\"模块【\" + row.moduleName + \"】删除成功\");\n      }).catch(() => {});\n    },\n    // 刷新模块详情\n    refreshModuleDetail(moduleId) {\n      if (!moduleId) return;\n      \n      // 获取最新的模块详情数据\n      getModule(moduleId).then(response => {\n        if (response.data) {\n          // 更新当前模块对象\n          this.currentModule = response.data;\n        }\n      }).catch(error => {\n        console.error(\"获取模块详情失败\", error);\n      });\n    },\n    // 切换全部展开/折叠\n    toggleExpandAll() {\n      this.isExpandAll = !this.isExpandAll;\n      this.$nextTick(() => {\n        if (this.isExpandAll) {\n          this.$refs.tree.expandAll();\n        } else {\n          this.$refs.tree.collapseAll();\n        }\n        \n        // 更新展开节点状态\n        this.saveExpandedKeys();\n        \n        // 显示成功提示\n        this.$message({\n          message: this.isExpandAll ? '已全部展开' : '已全部折叠',\n          type: 'success',\n          duration: 1500\n        });\n      });\n    },\n    /** 刷新树结构 */\n    refreshTree() {\n      console.log('刷新模块树结构');\n      // 显示加载中提示\n      this.$modal.loading(\"正在刷新树结构，请稍候...\");\n      \n      // 重新获取模块数据\n      listModule(this.queryParams).then(response => {\n        // 处理树形结构数据\n        this.moduleList = this.handleTree(response.data, \"moduleId\");\n        this.loading = false;\n        \n        // 关闭加载提示并显示成功消息\n        this.$modal.closeLoading();\n        this.$modal.msgSuccess(\"树结构刷新成功\");\n        \n        // 保持树的展开状态 - 放在成功提示之后延时执行，避免卡顿\n        setTimeout(() => {\n          if (this.$refs.tree) {\n            // 设置默认展开状态\n            this.$refs.tree.store.defaultExpandAll = this.isExpandAll;\n            \n            // 如果当前有选中的模块，刷新其详情\n            if (this.currentModule && this.currentModule.moduleId) {\n              this.refreshModuleDetail(this.currentModule.moduleId);\n            }\n          }\n        }, 100);\n      }).catch(error => {\n        console.error(\"刷新模块树结构失败:\", error);\n        this.$modal.closeLoading();\n        this.$modal.msgError(\"刷新树结构失败\");\n      });\n    },\n    // 保存当前所有展开节点的ID\n    saveExpandedKeys() {\n      if (this.$refs.tree) {\n        const expandedKeys = [];\n        \n        // 遍历所有节点，记录已展开的节点ID\n        const nodes = this.$refs.tree.store.nodesMap;\n        for (const key in nodes) {\n          if (nodes.hasOwnProperty(key) && nodes[key].expanded) {\n            expandedKeys.push(Number(key));\n          }\n        }\n        \n        this.expandedKeys = expandedKeys;\n        console.log('保存展开节点状态:', this.expandedKeys);\n      }\n    },\n    // 恢复节点展开状态\n    restoreExpandedKeys() {\n      if (this.$refs.tree) {\n        // 如果是展开全部的状态，全部展开\n        if (this.isExpandAll) {\n          this.$refs.tree.expandAll();\n          return;\n        }\n        \n        // 如果有记录展开节点，则恢复这些节点的展开状态\n        if (this.expandedKeys && this.expandedKeys.length > 0) {\n          console.log('恢复展开节点状态:', this.expandedKeys);\n          const nodes = this.$refs.tree.store.nodesMap;\n          \n          // 模拟点击每个保存的展开节点，触发展开\n          this.expandedKeys.forEach(key => {\n            if (nodes[key]) {\n              nodes[key].expanded = true;\n            }\n          });\n          \n          // 强制更新树视图\n          this.$refs.tree.store._getAllNodes().forEach(node => {\n            this.$refs.tree.store._setExpandedKeys(node);\n          });\n        }\n      }\n    },\n    // 导入模块\n    handleImport() {\n      this.importOpen = true;\n      this.resetImport();\n    },\n    // 重置导入状态\n    resetImport() {\n      this.fileList = [];\n      this.parsedModules = [];\n      this.selectedModules = [];\n      this.selectedParentModuleId = null;\n      this.parseLoading = false;\n      this.importLoading = false;\n      this.$refs.upload && this.$refs.upload.clearFiles();\n    },\n    // 文件数量超出限制\n    handleExceed() {\n      this.$modal.msgWarning('只能选择一个HTML文件');\n    },\n    // 上传前检查\n    beforeUpload(file) {\n      const isHTML = file.type === 'text/html' || file.name.toLowerCase().endsWith('.html');\n      const isLt10M = file.size / 1024 / 1024 < 10;\n\n      if (!isHTML) {\n        this.$modal.msgError('只能上传HTML文件!');\n        return false;\n      }\n      if (!isLt10M) {\n        this.$modal.msgError('上传文件大小不能超过 10MB!');\n        return false;\n      }\n      return false; // 阻止自动上传\n    },\n    // 解析文件\n    handleParseFile() {\n      const files = this.$refs.upload.uploadFiles;\n      if (!files || files.length === 0) {\n        this.$modal.msgWarning('请先选择HTML文件');\n        return;\n      }\n\n      this.parseLoading = true;\n      const file = files[0].raw;\n\n      parseHtmlFile(file).then(response => {\n        if (response.code === 200) {\n          this.parsedModules = response.data;\n          this.$modal.msgSuccess('文件解析成功，共解析出 ' + this.parsedModules.length + ' 个模块');\n        } else {\n          this.$modal.msgError(response.msg || '解析失败');\n        }\n      }).catch(error => {\n        console.error('解析文件失败:', error);\n        this.$modal.msgError('解析文件失败');\n      }).finally(() => {\n        this.parseLoading = false;\n      });\n    },\n    // 表格选择变化\n    handleSelectionChange(selection) {\n      this.selectedModules = selection;\n    },\n    // 检查行是否可选择\n    checkSelectable(row, index) {\n      return true; // 所有行都可选择\n    },\n    // 确认导入\n    handleConfirmImport() {\n      if (!this.parsedModules || this.parsedModules.length === 0) {\n        this.$modal.msgWarning('没有可导入的模块数据');\n        return;\n      }\n\n      // 获取选中的模块\n      const selectedModules = this.selectedModules && this.selectedModules.length > 0 ?\n        this.selectedModules : this.parsedModules;\n\n      if (!selectedModules || selectedModules.length === 0) {\n        this.$modal.msgWarning('请选择要导入的模块');\n        return;\n      }\n\n      this.$modal.confirm('确定要导入选中的 ' + selectedModules.length + ' 个模块吗？').then(() => {\n        this.importLoading = true;\n\n        // 构建导入参数\n        const importParams = {\n          modules: selectedModules,\n          parentModuleId: this.selectedParentModuleId\n        };\n\n        batchImportModules(importParams).then(response => {\n          if (response.code === 200) {\n            this.$modal.msgSuccess(response.msg || '导入成功');\n            this.importOpen = false;\n            this.getList(); // 刷新模块列表\n          } else {\n            this.$modal.msgError(response.msg || '导入失败');\n          }\n        }).catch(error => {\n          console.error('导入模块失败:', error);\n          this.$modal.msgError('导入模块失败');\n        }).finally(() => {\n          this.importLoading = false;\n        });\n      });\n    },\n    // 取消导入\n    cancelImport() {\n      this.importOpen = false;\n      this.resetImport();\n    }\n  }\n};\n</script> \n\n<style scoped>\n.mb8 {\n  margin-bottom: 8px;\n}\n\n.tree-container {\n  background-color: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  height: calc(100vh - 130px);\n  overflow: auto;\n}\n\n.tree-header {\n  padding: 10px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.tree-actions {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 8px;\n}\n\n.module-detail {\n  margin-top: 10px;\n}\n\n.empty-tip {\n  padding: 40px 0;\n  text-align: center;\n}\n\n.custom-tree-node {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  font-size: 14px;\n  padding-right: 8px;\n}\n\n.card-title {\n  font-size: 15px;\n  font-weight: bold;\n}\n\n.import-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n  padding: 10px;\n}\n\n.parent-module-selector {\n  margin-bottom: 15px;\n  padding: 10px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n</style> "]}]}