{"remainingRequest": "D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ruoyi\\Zhike\\zhike-ui\\src\\views\\system\\module\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\ruoyi\\Zhike\\zhike-ui\\src\\views\\system\\module\\index.vue", "mtime": 1754215759744}, {"path": "D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737340298796}, {"path": "D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737340300972}, {"path": "D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737340298796}, {"path": "D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737340301724}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RNb2R1bGUsIGdldE1vZHVsZSwgZGVsTW9kdWxlLCBhZGRNb2R1bGUsIHVwZGF0ZU1vZHVsZSwgcGFyc2VIdG1sRmlsZSwgYmF0Y2hJbXBvcnRNb2R1bGVzIH0gZnJvbSAiQC9hcGkvc3lzdGVtL21vZHVsZSI7CmltcG9ydCB7IGxpc3RDb21wZXRpdG9yIH0gZnJvbSAiQC9hcGkvY29tcGV0aXRvci9jb21wZXRpdG9yIjsKaW1wb3J0IFRyZWVzZWxlY3QgZnJvbSAiQHJpb3BoYWUvdnVlLXRyZWVzZWxlY3QiOwppbXBvcnQgIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0L2Rpc3QvdnVlLXRyZWVzZWxlY3QuY3NzIjsKaW1wb3J0IHsgcGlueWluIH0gZnJvbSAncGlueWluLXBybyc7CgovLyDmt7vliqDmsYnlrZfovazmi7zpn7PnmoTlt6Xlhbflh73mlbAKZnVuY3Rpb24gY29udmVydFRvUGlueWluKGNoaW5lc2UpIHsKICBpZiAoIWNoaW5lc2UpIHJldHVybiAnJzsKICBjb25zb2xlLmxvZygn6L6T5YWl55qE5Lit5paH77yaJywgY2hpbmVzZSk7CiAgCiAgLy8g5bCG5rGJ5a2X6L2s5o2i5Li65ou86Z+z77yM5bm26L2s5o2i5Li65aSn5YaZCiAgY29uc3QgcGlueWluUmVzdWx0ID0gcGlueWluKGNoaW5lc2UsIHsKICAgIHRvbmVUeXBlOiAnbm9uZScsICAgIC8vIOS4jeW4puWjsOiwgwogICAgdHlwZTogJ2FycmF5JywgICAgICAgLy8g6L+U5<PERSON>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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0j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file": "index.vue", "sourceRoot": "src/views/system/module", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 搜索区域 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" class=\"mb8\">\n      <el-form-item label=\"模块名称\" prop=\"moduleName\">\n        <el-input\n          v-model=\"queryParams.moduleName\"\n          placeholder=\"请输入模块名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"模块编码\" prop=\"moduleCode\">\n        <el-input\n          v-model=\"queryParams.moduleCode\"\n          placeholder=\"请输入模块编码\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"业务类型\" prop=\"businessType\">\n        <el-select v-model=\"queryParams.businessType\" placeholder=\"请选择业务类型\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.product_plan_type\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"所属厂商\" prop=\"competitorId\">\n        <el-select v-model=\"queryParams.competitorId\" placeholder=\"请选择厂商\" clearable filterable>\n          <el-option\n            v-for=\"item in competitorOptions\"\n            :key=\"item.id\"\n            :label=\"item.name\"\n            :value=\"item.id\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"模块类型\" prop=\"moduleType\">\n        <el-select v-model=\"queryParams.moduleType\" placeholder=\"模块类型\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_module_type\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"模块状态\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_normal_disable\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"模块性质\" prop=\"nature\">\n        <el-select v-model=\"queryParams.nature\" placeholder=\"请选择模块性质\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_module_nature\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"报价类型\" prop=\"quotationNature\">\n        <el-select v-model=\"queryParams.quotationNature\" placeholder=\"请选择报价类型\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_quotation_nature\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"是否BD\" prop=\"isBd\">\n        <el-select v-model=\"queryParams.isBd\" placeholder=\"请选择是否BD\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_yes_no\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"医院评级\" prop=\"hospitalCertification\">\n        <el-select v-model=\"queryParams.hospitalCertification\" placeholder=\"请选择医院评级\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.hospital_certification\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n\n      <el-form-item label=\"价格范围\" prop=\"priceRange\">\n        <el-input-number\n          v-model=\"queryParams.minPrice\"\n          placeholder=\"最小价格\"\n          :min=\"0\"\n          :precision=\"2\"\n          style=\"width: 120px\"\n        />\n        <span style=\"margin: 0 10px\">-</span>\n        <el-input-number\n          v-model=\"queryParams.maxPrice\"\n          placeholder=\"最大价格\"\n          :min=\"0\"\n          :precision=\"2\"\n          style=\"width: 120px\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"20\">\n      <!-- 左侧树形结构 -->\n      <el-col :span=\"6\" class=\"tree-container\">\n        <div class=\"tree-header\">\n          <el-input\n            v-model=\"filterText\"\n            placeholder=\"请输入关键字进行过滤\"\n            clearable\n            prefix-icon=\"el-icon-search\"\n            size=\"small\"\n          />\n          <div class=\"tree-actions\">\n            <el-button\n              type=\"text\"\n              icon=\"el-icon-sort\"\n              size=\"mini\"\n              @click=\"toggleExpandAll\"\n            >{{ isExpandAll ? '全部折叠' : '全部展开' }}</el-button>\n            <el-button\n              type=\"text\"\n              icon=\"el-icon-refresh\"\n              size=\"mini\"\n              @click=\"refreshTree\"\n            >刷新树</el-button>\n            <el-button\n              type=\"text\"\n              icon=\"el-icon-search\"\n              size=\"mini\"\n              @click=\"showSearch = !showSearch\"\n            >{{ showSearch ? '收起搜索' : '高级搜索' }}</el-button>\n          </div>\n        </div>\n        <el-tree\n          ref=\"tree\"\n          :data=\"moduleList\"\n          :props=\"defaultProps\"\n          :filter-node-method=\"filterNode\"\n          :expand-on-click-node=\"false\"\n          node-key=\"moduleId\"\n          :default-expand-all=\"isExpandAll\"\n          highlight-current\n          @node-click=\"handleNodeClick\"\n        >\n          <span class=\"custom-tree-node\" slot-scope=\"{ node, data }\">\n            <span>\n              <i class=\"el-icon-folder\" style=\"margin-right: 5px\"></i>\n              {{ node.label }}\n            </span>\n          </span>\n        </el-tree>\n      </el-col>\n\n      <!-- 右侧内容区域 -->\n      <el-col :span=\"18\">\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span class=\"card-title\">{{ currentModule.moduleName || '请选择模块' }}</span>\n            <el-button-group style=\"float: right\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['system:module:add']\"\n        >新增</el-button>\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-upload2\"\n          size=\"mini\"\n          @click=\"handleImport\"\n          v-hasPermi=\"['system:module:add']\"\n        >导入模块</el-button>\n              <el-button\n                type=\"success\"\n                plain\n                icon=\"el-icon-edit\"\n                size=\"mini\"\n                @click=\"handleUpdate(currentModule)\"\n                v-hasPermi=\"['system:module:edit']\"\n                v-if=\"currentModule.moduleId\"\n              >修改</el-button>\n              <el-button\n                type=\"danger\"\n                plain\n                icon=\"el-icon-delete\"\n                size=\"mini\"\n                @click=\"handleDelete(currentModule)\"\n                v-hasPermi=\"['system:module:remove']\"\n                v-if=\"currentModule.moduleId\"\n              >删除</el-button>\n        <el-button\n          type=\"info\"\n          plain\n                icon=\"el-icon-plus\"\n          size=\"mini\"\n                @click=\"handleAdd(currentModule)\"\n                v-hasPermi=\"['system:module:add']\"\n                v-if=\"currentModule.moduleId\"\n              >添加子模块</el-button>\n            </el-button-group>\n          </div>\n\n          <!-- 模块详情内容 -->\n          <div v-if=\"currentModule.moduleId\" class=\"module-detail\">\n            <el-descriptions :column=\"2\" border>\n              <el-descriptions-item label=\"模块名称\">{{ currentModule.moduleName }}</el-descriptions-item>\n              <el-descriptions-item label=\"模块编码\">{{ currentModule.moduleCode }}</el-descriptions-item>\n              <el-descriptions-item label=\"显示顺序\">{{ currentModule.orderNum }}</el-descriptions-item>\n              <el-descriptions-item label=\"上级模块\">{{ getParentModuleName(currentModule.parentId) }}</el-descriptions-item>\n              <el-descriptions-item label=\"模块类型\">\n                <dict-tag :options=\"dict.type.sys_module_type\" :value=\"currentModule.moduleType\"/>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"模块属性\">\n                <dict-tag :options=\"dict.type.sys_module_property\" :value=\"currentModule.moduleProperty\"/>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"所属厂商\">{{ currentModule.competitorName }}</el-descriptions-item>\n              <el-descriptions-item label=\"模块状态\">\n                <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"currentModule.status\"/>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"性质\">\n                <dict-tag :options=\"dict.type.sys_module_nature\" :value=\"currentModule.nature\"/>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"报价性质\">\n                <dict-tag :options=\"dict.type.sys_quotation_nature\" :value=\"currentModule.quotationNature\"/>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"是否BD\">\n                <dict-tag :options=\"dict.type.sys_yes_no\" :value=\"currentModule.isBd\"/>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"医院评级\">\n                <dict-tag :options=\"dict.type.hospital_certification\" :value=\"currentModule.hospitalCertification\"/>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"模块说明\">{{ currentModule.moduleDesc || '-' }}</el-descriptions-item>\n              <el-descriptions-item label=\"业务类型\">\n                <dict-tag :options=\"dict.type.product_plan_type\" :value=\"currentModule.businessType\"/>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"价格\">\n                <span v-if=\"currentModule.price\">¥{{ currentModule.price }}</span>\n                <span v-else>-</span>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"创建时间\">{{ parseTime(currentModule.createTime) }}</el-descriptions-item>\n              <el-descriptions-item label=\"更新时间\">{{ parseTime(currentModule.updateTime) }}</el-descriptions-item>\n              <el-descriptions-item label=\"备注\" :span=\"2\">{{ currentModule.remark }}</el-descriptions-item>\n            </el-descriptions>\n          </div>\n          <div v-else class=\"empty-tip\">\n            <el-empty description=\"请从左侧选择模块\"></el-empty>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 添加或修改系统模块对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"上级模块\" prop=\"parentId\">\n              <treeselect \n                v-model=\"form.parentId\" \n                :options=\"moduleOptions\" \n                :normalizer=\"normalizer\" \n                placeholder=\"选择上级模块\"\n                :disabled=\"form.parentId === 0\" \n                :clearable=\"false\"\n                :default-expand-level=\"1\"\n                :show-count=\"true\"\n                noOptionsMessage=\"无\"\n                noResultsText=\"无\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"模块名称\" prop=\"moduleName\">\n              <el-input v-model=\"form.moduleName\" placeholder=\"请输入模块名称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"模块编码\" prop=\"moduleCode\">\n              <el-input v-model=\"form.moduleCode\" placeholder=\"请输入模块编码\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"显示排序\" prop=\"orderNum\">\n              <el-input-number v-model=\"form.orderNum\" controls-position=\"right\" :min=\"0\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"模块类型\" prop=\"moduleType\">\n              <el-select v-model=\"form.moduleType\" placeholder=\"请选择\">\n                <el-option\n                  v-for=\"dict in dict.type.sys_module_type\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"模块属性\" prop=\"moduleProperty\">\n              <el-select v-model=\"form.moduleProperty\" placeholder=\"请选择\">\n                <el-option\n                  v-for=\"dict in dict.type.sys_module_property\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"所属厂商\" prop=\"competitorId\">\n              <el-select\n                v-model=\"form.competitorId\"\n                placeholder=\"请选择厂商\"\n                filterable\n                clearable\n              >\n                <el-option\n                  v-for=\"item in competitorOptions\"\n                  :key=\"item.id\"\n                  :label=\"item.name\"\n                  :value=\"item.id\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"模块状态\">\n              <el-radio-group v-model=\"form.status\">\n                <el-radio\n                  v-for=\"dict in dict.type.sys_normal_disable\"\n                  :key=\"dict.value\"\n                  :label=\"dict.value\"\n                >{{dict.label}}</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"性质\" prop=\"nature\">\n              <el-select v-model=\"form.nature\" placeholder=\"请选择\">\n                <el-option\n                  v-for=\"dict in dict.type.sys_module_nature\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"报价性质\" prop=\"quotationNature\">\n              <el-select v-model=\"form.quotationNature\" placeholder=\"请选择\">\n                <el-option\n                  v-for=\"dict in dict.type.sys_quotation_nature\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否BD\" prop=\"isBd\">\n              <el-select v-model=\"form.isBd\" placeholder=\"请选择\">\n                <el-option\n                  v-for=\"dict in dict.type.sys_yes_no\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n                     <el-col :span=\"12\">\n             <el-form-item label=\"医院评级\" prop=\"hospitalCertification\">\n               <el-select v-model=\"form.hospitalCertification\" placeholder=\"请选择\">\n                 <el-option\n                   v-for=\"dict in dict.type.hospital_certification\"\n                   :key=\"dict.value\"\n                   :label=\"dict.label\"\n                   :value=\"dict.value\"\n                 ></el-option>\n               </el-select>\n             </el-form-item>\n           </el-col>\n           <el-col :span=\"12\">\n             <el-form-item label=\"价格\" prop=\"price\">\n               <el-input-number\n                 v-model=\"form.price\"\n                 placeholder=\"请输入价格\"\n                 :min=\"0\"\n                 :precision=\"2\"\n                 controls-position=\"right\"\n                 style=\"width: 100%\"\n               />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"业务类型\" prop=\"businessType\">\n              <el-select v-model=\"form.businessType\" placeholder=\"请选择业务类型\">\n                <el-option\n                  v-for=\"dict in dict.type.product_plan_type\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"模块说明\" prop=\"moduleDesc\">\n              <el-input v-model=\"form.moduleDesc\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入模块说明\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"remark\">\n              <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入备注\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\" :loading=\"formLoading\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 模块导入对话框 -->\n    <el-dialog title=\"模块导入\" :visible.sync=\"importOpen\" width=\"800px\" append-to-body>\n      <div v-if=\"!parsedModules.length\">\n        <el-upload\n          ref=\"upload\"\n          action=\"#\"\n          :limit=\"1\"\n          accept=\".html\"\n          :on-exceed=\"handleExceed\"\n          :before-upload=\"beforeUpload\"\n          :auto-upload=\"false\"\n          :file-list=\"fileList\"\n          drag\n        >\n          <i class=\"el-icon-upload\"></i>\n          <div class=\"el-upload__text\">将HTML文件拖到此处，或<em>点击上传</em></div>\n          <div class=\"el-upload__tip\" slot=\"tip\">只能上传HTML文件，且不超过10MB</div>\n        </el-upload>\n        <div style=\"margin-top: 20px; text-align: center\">\n          <el-button type=\"primary\" @click=\"handleParseFile\" :loading=\"parseLoading\">解析文件</el-button>\n        </div>\n      </div>\n\n      <div v-else>\n        <div class=\"import-header\">\n          <span>解析结果：共找到 {{ parsedModules.length }} 个模块</span>\n          <el-button type=\"text\" @click=\"resetImport\">重新选择文件</el-button>\n        </div>\n\n        <!-- 父模块选择 -->\n        <div class=\"parent-module-selector\">\n          <el-form :inline=\"true\">\n            <el-form-item label=\"导入到父模块：\">\n              <treeselect\n                v-model=\"selectedParentModuleId\"\n                :options=\"moduleOptions\"\n                :normalizer=\"normalizer\"\n                :show-count=\"true\"\n                placeholder=\"选择父模块（可选）\"\n                :clearable=\"true\"\n                style=\"width: 300px\"\n              />\n            </el-form-item>\n          </el-form>\n        </div>\n\n        <el-table\n          ref=\"importTable\"\n          :data=\"parsedModules\"\n          border\n          style=\"width: 100%; margin-top: 10px\"\n          max-height=\"400\"\n          @selection-change=\"handleSelectionChange\"\n        >\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" :selectable=\"checkSelectable\" />\n          <el-table-column prop=\"moduleName\" label=\"模块名称\" min-width=\"200\">\n            <template slot-scope=\"scope\">\n              <span\n                :style=\"{\n                  'margin-left': (scope.row.level - 1) * 20 + 'px',\n                  'font-weight': scope.row.isParent ? 'bold' : 'normal',\n                  'color': scope.row.isParent ? '#409EFF' : '#606266'\n                }\"\n              >\n                <i v-if=\"scope.row.isParent\" class=\"el-icon-folder\" style=\"margin-right: 5px\"></i>\n                <i v-else class=\"el-icon-document\" style=\"margin-right: 5px\"></i>\n                {{ scope.row.moduleName }}\n              </span>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"moduleCode\" label=\"模块编码\" width=\"150\" />\n          <el-table-column prop=\"parentName\" label=\"父模块\" width=\"150\">\n            <template slot-scope=\"scope\">\n              {{ scope.row.parentName || '无' }}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"level\" label=\"层级\" width=\"60\">\n            <template slot-scope=\"scope\">\n              <el-tag :type=\"scope.row.level === 1 ? 'primary' : scope.row.level === 2 ? 'success' : 'warning'\" size=\"mini\">\n                {{ scope.row.level || 1 }}级\n              </el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"orderNum\" label=\"排序\" width=\"80\" />\n        </el-table>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button v-if=\"parsedModules.length\" type=\"primary\" @click=\"handleConfirmImport\" :loading=\"importLoading\">\n          确认导入\n        </el-button>\n        <el-button @click=\"cancelImport\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listModule, getModule, delModule, addModule, updateModule, parseHtmlFile, batchImportModules } from \"@/api/system/module\";\nimport { listCompetitor } from \"@/api/competitor/competitor\";\nimport Treeselect from \"@riophae/vue-treeselect\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\nimport { pinyin } from 'pinyin-pro';\n\n// 添加汉字转拼音的工具函数\nfunction convertToPinyin(chinese) {\n  if (!chinese) return '';\n  console.log('输入的中文：', chinese);\n  \n  // 将汉字转换为拼音，并转换为大写\n  const pinyinResult = pinyin(chinese, {\n    toneType: 'none',    // 不带声调\n    type: 'array',       // 返回拼音数组\n    nonZh: 'consecutive', // 非汉字连续返回\n    pattern: 'first'     // 只取首字母\n  });\n  console.log('pinyin转换结果：', pinyinResult);\n  \n  const upperResult = pinyinResult.map(py => py.toUpperCase());\n  console.log('转大写结果：', upperResult);\n  \n  const finalResult = upperResult.join('');\n  console.log('最终结果：', finalResult);\n  \n  return finalResult;\n}\n\nexport default {\n  name: \"Module\",\n  dicts: ['sys_normal_disable', 'sys_module_type', 'sys_module_property', 'sys_module_nature', 'sys_quotation_nature', 'sys_yes_no', 'hospital_certification', 'product_plan_type'],\n  components: { Treeselect },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 表单加载状态\n      formLoading: false,\n      // 表格树数据\n      moduleList: [],\n      // 模块树选项\n      moduleOptions: [],\n      // 厂商选项\n      competitorOptions: [],\n      // 默认卫宁厂商ID\n      defaultCompetitorId: null,\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 是否展开，默认全部展开\n      isExpandAll: false,\n      // 重新渲染表格状态\n      refreshTable: true,\n      // 查询参数\n      queryParams: {\n        moduleName: undefined,\n        moduleCode: undefined,\n        moduleType: undefined,\n        status: undefined,\n        competitorId: undefined,\n        nature: undefined,\n        quotationNature: undefined,\n        isBd: undefined,\n        hospitalCertification: undefined,\n        minPrice: undefined,\n        maxPrice: undefined,\n        businessType: undefined\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        moduleName: [\n          { required: true, message: \"模块名称不能为空\", trigger: \"blur\" }\n        ],\n        orderNum: [\n          { required: true, message: \"显示排序不能为空\", trigger: \"blur\" }\n        ],\n        moduleType: [\n          { required: true, message: \"模块类型不能为空\", trigger: \"blur\" }\n        ],\n        moduleProperty: [\n          { required: true, message: \"模块属性不能为空\", trigger: \"blur\" }\n        ],\n        competitorId: [\n          { required: true, message: \"请选择所属厂商\", trigger: \"change\" }\n        ],\n        businessType: [\n          { required: true, message: \"请选择业务类型\", trigger: \"change\" }\n        ]\n      },\n      // 树形结构相关\n      filterText: '',\n      defaultProps: {\n        children: 'children',\n        label: 'moduleName'\n      },\n      currentModule: {}, // 当前选中的模块\n      showSearch: false, // 默认不显示搜索条件\n      expandedKeys: [], // 当前展开的节点ID列表\n      // 导入相关\n      importOpen: false, // 导入对话框是否显示\n      parseLoading: false, // 解析加载状态\n      importLoading: false, // 导入加载状态\n      fileList: [], // 上传文件列表\n      parsedModules: [], // 解析出的模块数据\n      selectedModules: [], // 选中的模块数据\n      selectedParentModuleId: null, // 选择的父模块ID\n    };\n  },\n  watch: {\n    // 监听模块名称变化，自动生成模块编码\n    'form.moduleName': {\n      handler(newVal) {\n        // 在新增或编辑时都自动生成编码\n        if (newVal) {\n          console.log('模块名称变化：', newVal);\n          this.form.moduleCode = convertToPinyin(newVal);\n          console.log('生成的模块编码：', this.form.moduleCode);\n        }\n      }\n    },\n    // 监听上级模块变化，重新获取显示排序\n    'form.parentId': {\n      handler(newVal) {\n        // 如果是新增模式（没有moduleId）并且选择了有效的上级模块\n        if (!this.form.moduleId && newVal !== undefined) {\n          this.getMaxOrderNum(newVal);\n        }\n      }\n    },\n    // 监听过滤文本变化\n    filterText(val) {\n      this.$refs.tree.filter(val);\n    }\n  },\n  created() {\n    this.getList();\n    this.getCompetitorOptions();\n  },\n  methods: {\n    /** 查询模块列表 */\n    getList() {\n      this.loading = true;\n      \n      // 保存当前展开的节点ID\n      this.saveExpandedKeys();\n      \n      // 确保将厂商ID传递给后端接口\n      let params = Object.assign({}, this.queryParams);\n      \n      listModule(params).then(response => {\n        this.moduleList = this.handleTree(response.data, \"moduleId\");\n        this.loading = false;\n        \n        // 恢复展开的节点\n        this.$nextTick(() => {\n          this.restoreExpandedKeys();\n        });\n      });\n    },\n    /** 查询厂商选项 */\n    getCompetitorOptions() {\n      listCompetitor({ status: '0', pageSize: 9999 }).then(response => {\n        this.competitorOptions = response.rows;\n      });\n    },\n    /** 转换模块数据结构 */\n    normalizer(node) {\n      if (node.children && !node.children.length) {\n        delete node.children;\n      }\n      return {\n        id: node.moduleId,\n        label: node.moduleName,\n        children: node.children\n      };\n    },\n    // 过滤节点\n    filterNode(value, data) {\n      if (!value) return true;\n      return data.moduleName.toLowerCase().indexOf(value.toLowerCase()) !== -1;\n    },\n    // 节点点击事件\n    handleNodeClick(data) {\n      this.currentModule = data;\n    },\n    // 获取父模块名称\n    getParentModuleName(parentId) {\n      if (!parentId || parentId === 0) {\n        return '无';\n      }\n      \n      // 递归在模块树中查找指定ID的模块\n      const findModule = (list, id) => {\n        for (const item of list) {\n          if (item.moduleId === id) {\n            return item.moduleName;\n          }\n          if (item.children && item.children.length > 0) {\n            const found = findModule(item.children, id);\n            if (found) return found;\n          }\n        }\n        return null;\n      };\n      \n      const name = findModule(this.moduleList, parentId);\n      return name || '未知';\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        moduleId: undefined,\n        parentId: undefined,\n        moduleName: undefined,\n        moduleCode: undefined,\n        orderNum: 99, // 设置默认排序为99\n        moduleType: \"1\",  // 修改默认值为系统模块\n        moduleProperty: \"3\", // 默认为功能\n        competitorId: undefined, // 取消默认厂商设置\n        status: \"0\",\n        nature: undefined,\n        quotationNature: undefined,\n        isBd: undefined,\n        hospitalCertification: undefined,\n        price: undefined,\n        remark: undefined,\n        moduleDesc: undefined,\n        businessType: undefined\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    /** 新增按钮操作 */\n    handleAdd(row) {\n      this.reset();\n      if (row && row.moduleId) {\n        this.form.parentId = row.moduleId;\n        // 如果是子模块，继承父模块的厂商ID\n        if (row.competitorId) {\n          this.form.competitorId = row.competitorId;\n        }\n        \n        // 获取同级模块的最大排序号\n        this.getMaxOrderNum(row.moduleId);\n      } else {\n        // 获取顶级模块的最大排序号\n        this.getMaxOrderNum(0);\n      }\n      this.open = true;\n      this.title = \"添加模块\";\n      listModule().then(response => {\n        this.moduleOptions = this.handleTree(response.data, \"moduleId\");\n      });\n    },\n    /** 获取同级模块中的最大排序号 */\n    getMaxOrderNum(parentId) {\n      // 明确查询同一父模块下的直接子模块\n      listModule({ parentId: parentId }).then(response => {\n        if (response.data && response.data.length > 0) {\n          // 过滤出具有相同parentId的模块，确保只处理真正的同级模块\n          const siblingModules = response.data.filter(item => item.parentId === parentId);\n          \n          if (siblingModules.length > 0) {\n            // 找出同级模块中的最大排序号\n            const maxOrderNum = Math.max(...siblingModules.map(m => m.orderNum || 0));\n            // 设置新模块的排序号为最大值+1\n            this.form.orderNum = maxOrderNum + 1;\n            console.log('获取到同级模块最大排序号：', maxOrderNum, '设置新模块排序号为：', this.form.orderNum);\n          } else {\n            // 如果该父模块下没有子模块，则设置排序号为1\n            this.form.orderNum = 1;\n            console.log('该父模块下无子模块，设置新模块排序号为：1');\n          }\n        } else {\n          // 如果没有返回数据，则设置排序号为1\n          this.form.orderNum = 1;\n          console.log('查询无返回数据，设置新模块排序号为：1');\n        }\n      }).catch(error => {\n        console.error('获取同级模块排序号失败：', error);\n        // 发生错误时设置默认排序号\n        this.form.orderNum = 1;\n      });\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      if (!row || !row.moduleId) {\n        this.$modal.msgError(\"请先选择要修改的模块\");\n        return;\n      }\n      \n      getModule(row.moduleId).then(response => {\n        this.form = response.data;\n        // 如果是顶级模块(parentId=0),则禁用上级模块选择\n        if (this.form.parentId === 0) {\n          this.$nextTick(() => {\n            this.$refs.form.clearValidate('parentId');\n            this.$set(this.rules, 'parentId', []);\n          });\n          // 添加一个顶级节点选项\n          this.moduleOptions = [{\n            id: 0,\n            label: '无'\n          }];\n        } else {\n          listModule().then(response => {\n            this.moduleOptions = this.handleTree(response.data, \"moduleId\");\n          });\n        }\n        this.open = true;\n        this.title = \"修改模块\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm: function() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          this.formLoading = true;\n          \n          // 保存当前展开的节点ID\n          this.saveExpandedKeys();\n          \n          // 记录当前表单的父节点ID和本身ID\n          const parentId = this.form.parentId;\n          const moduleId = this.form.moduleId;\n          \n          if (moduleId != undefined) {\n            updateModule(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              \n              // 将当前节点ID添加到展开节点列表\n              if (!this.expandedKeys.includes(moduleId) && moduleId !== 0) {\n                this.expandedKeys.push(moduleId);\n              }\n              \n              // 将父节点ID添加到展开节点列表\n              if (parentId && !this.expandedKeys.includes(parentId) && parentId !== 0) {\n                this.expandedKeys.push(parentId);\n              }\n              \n              this.getList();\n              \n              // 如果是当前模块，也要刷新详情\n              if (this.currentModule.moduleId === moduleId) {\n                this.refreshModuleDetail(moduleId);\n              }\n              \n              this.formLoading = false;\n            }).catch(() => {\n              this.formLoading = false;\n            });\n          } else {\n            addModule(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              \n              // 确保父节点在展开列表中\n              if (parentId && !this.expandedKeys.includes(parentId) && parentId !== 0) {\n                this.expandedKeys.push(parentId);\n              }\n              \n              // 如果是新增的根节点，记录其ID\n              if (response.data && response.data.moduleId && parentId === 0) {\n                const newModuleId = response.data.moduleId;\n                this.expandedKeys.push(newModuleId);\n              }\n              \n              this.getList();\n              this.formLoading = false;\n            }).catch(() => {\n              this.formLoading = false;\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      if (!row || !row.moduleId) {\n        this.$modal.msgError(\"未选择要删除的模块\");\n        return;\n      }\n      \n      // 检查是否有子节点\n      if (row.children && row.children.length > 0) {\n        this.$modal.msgError('该模块下存在子模块，请先删除子模块');\n        return;\n      }\n      \n      this.$modal.confirm('确定要删除模块【' + row.moduleName + '】吗？删除后无法恢复！').then(() => {\n        return delModule(row.moduleId);\n      }).then(() => {\n        // 记录当前删除的模块ID\n        const deletedModuleId = row.moduleId;\n        \n        // 刷新模块列表\n        this.getList();\n        \n        // 如果右侧当前显示的就是被删除的模块，则清空右侧内容\n        if (this.currentModule && this.currentModule.moduleId === deletedModuleId) {\n          this.currentModule = {};\n        }\n        \n        this.$modal.msgSuccess(\"模块【\" + row.moduleName + \"】删除成功\");\n      }).catch(() => {});\n    },\n    // 刷新模块详情\n    refreshModuleDetail(moduleId) {\n      if (!moduleId) return;\n      \n      // 获取最新的模块详情数据\n      getModule(moduleId).then(response => {\n        if (response.data) {\n          // 更新当前模块对象\n          this.currentModule = response.data;\n        }\n      }).catch(error => {\n        console.error(\"获取模块详情失败\", error);\n      });\n    },\n    // 切换全部展开/折叠\n    toggleExpandAll() {\n      this.isExpandAll = !this.isExpandAll;\n      this.$nextTick(() => {\n        if (this.isExpandAll) {\n          this.$refs.tree.expandAll();\n        } else {\n          this.$refs.tree.collapseAll();\n        }\n        \n        // 更新展开节点状态\n        this.saveExpandedKeys();\n        \n        // 显示成功提示\n        this.$message({\n          message: this.isExpandAll ? '已全部展开' : '已全部折叠',\n          type: 'success',\n          duration: 1500\n        });\n      });\n    },\n    /** 刷新树结构 */\n    refreshTree() {\n      console.log('刷新模块树结构');\n      // 显示加载中提示\n      this.$modal.loading(\"正在刷新树结构，请稍候...\");\n      \n      // 重新获取模块数据\n      listModule(this.queryParams).then(response => {\n        // 处理树形结构数据\n        this.moduleList = this.handleTree(response.data, \"moduleId\");\n        this.loading = false;\n        \n        // 关闭加载提示并显示成功消息\n        this.$modal.closeLoading();\n        this.$modal.msgSuccess(\"树结构刷新成功\");\n        \n        // 保持树的展开状态 - 放在成功提示之后延时执行，避免卡顿\n        setTimeout(() => {\n          if (this.$refs.tree) {\n            // 设置默认展开状态\n            this.$refs.tree.store.defaultExpandAll = this.isExpandAll;\n            \n            // 如果当前有选中的模块，刷新其详情\n            if (this.currentModule && this.currentModule.moduleId) {\n              this.refreshModuleDetail(this.currentModule.moduleId);\n            }\n          }\n        }, 100);\n      }).catch(error => {\n        console.error(\"刷新模块树结构失败:\", error);\n        this.$modal.closeLoading();\n        this.$modal.msgError(\"刷新树结构失败\");\n      });\n    },\n    // 保存当前所有展开节点的ID\n    saveExpandedKeys() {\n      if (this.$refs.tree) {\n        const expandedKeys = [];\n        \n        // 遍历所有节点，记录已展开的节点ID\n        const nodes = this.$refs.tree.store.nodesMap;\n        for (const key in nodes) {\n          if (nodes.hasOwnProperty(key) && nodes[key].expanded) {\n            expandedKeys.push(Number(key));\n          }\n        }\n        \n        this.expandedKeys = expandedKeys;\n        console.log('保存展开节点状态:', this.expandedKeys);\n      }\n    },\n    // 恢复节点展开状态\n    restoreExpandedKeys() {\n      if (this.$refs.tree) {\n        // 如果是展开全部的状态，全部展开\n        if (this.isExpandAll) {\n          this.$refs.tree.expandAll();\n          return;\n        }\n        \n        // 如果有记录展开节点，则恢复这些节点的展开状态\n        if (this.expandedKeys && this.expandedKeys.length > 0) {\n          console.log('恢复展开节点状态:', this.expandedKeys);\n          const nodes = this.$refs.tree.store.nodesMap;\n          \n          // 模拟点击每个保存的展开节点，触发展开\n          this.expandedKeys.forEach(key => {\n            if (nodes[key]) {\n              nodes[key].expanded = true;\n            }\n          });\n          \n          // 强制更新树视图\n          this.$refs.tree.store._getAllNodes().forEach(node => {\n            this.$refs.tree.store._setExpandedKeys(node);\n          });\n        }\n      }\n    },\n    // 导入模块\n    handleImport() {\n      this.importOpen = true;\n      this.resetImport();\n    },\n    // 重置导入状态\n    resetImport() {\n      this.fileList = [];\n      this.parsedModules = [];\n      this.selectedModules = [];\n      this.selectedParentModuleId = null;\n      this.parseLoading = false;\n      this.importLoading = false;\n      this.$refs.upload && this.$refs.upload.clearFiles();\n    },\n    // 文件数量超出限制\n    handleExceed() {\n      this.$modal.msgWarning('只能选择一个HTML文件');\n    },\n    // 上传前检查\n    beforeUpload(file) {\n      const isHTML = file.type === 'text/html' || file.name.toLowerCase().endsWith('.html');\n      const isLt10M = file.size / 1024 / 1024 < 10;\n\n      if (!isHTML) {\n        this.$modal.msgError('只能上传HTML文件!');\n        return false;\n      }\n      if (!isLt10M) {\n        this.$modal.msgError('上传文件大小不能超过 10MB!');\n        return false;\n      }\n      return false; // 阻止自动上传\n    },\n    // 解析文件\n    handleParseFile() {\n      const files = this.$refs.upload.uploadFiles;\n      if (!files || files.length === 0) {\n        this.$modal.msgWarning('请先选择HTML文件');\n        return;\n      }\n\n      this.parseLoading = true;\n      const file = files[0].raw;\n\n      parseHtmlFile(file).then(response => {\n        if (response.code === 200) {\n          this.parsedModules = response.data;\n          this.$modal.msgSuccess('文件解析成功，共解析出 ' + this.parsedModules.length + ' 个模块');\n        } else {\n          this.$modal.msgError(response.msg || '解析失败');\n        }\n      }).catch(error => {\n        console.error('解析文件失败:', error);\n        this.$modal.msgError('解析文件失败');\n      }).finally(() => {\n        this.parseLoading = false;\n      });\n    },\n    // 表格选择变化\n    handleSelectionChange(selection) {\n      this.selectedModules = selection;\n    },\n    // 检查行是否可选择\n    checkSelectable(row, index) {\n      return true; // 所有行都可选择\n    },\n    // 确认导入\n    handleConfirmImport() {\n      if (!this.parsedModules || this.parsedModules.length === 0) {\n        this.$modal.msgWarning('没有可导入的模块数据');\n        return;\n      }\n\n      // 获取选中的模块\n      const selectedModules = this.selectedModules && this.selectedModules.length > 0 ?\n        this.selectedModules : this.parsedModules;\n\n      if (!selectedModules || selectedModules.length === 0) {\n        this.$modal.msgWarning('请选择要导入的模块');\n        return;\n      }\n\n      this.$modal.confirm('确定要导入选中的 ' + selectedModules.length + ' 个模块吗？').then(() => {\n        this.importLoading = true;\n\n        // 构建导入参数\n        const importParams = {\n          modules: selectedModules,\n          parentModuleId: this.selectedParentModuleId\n        };\n\n        batchImportModules(importParams).then(response => {\n          if (response.code === 200) {\n            this.$modal.msgSuccess(response.msg || '导入成功');\n            this.importOpen = false;\n            this.getList(); // 刷新模块列表\n          } else {\n            this.$modal.msgError(response.msg || '导入失败');\n          }\n        }).catch(error => {\n          console.error('导入模块失败:', error);\n          this.$modal.msgError('导入模块失败');\n        }).finally(() => {\n          this.importLoading = false;\n        });\n      });\n    },\n    // 取消导入\n    cancelImport() {\n      this.importOpen = false;\n      this.resetImport();\n    }\n  }\n};\n</script> \n\n<style scoped>\n.mb8 {\n  margin-bottom: 8px;\n}\n\n.tree-container {\n  background-color: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  height: calc(100vh - 130px);\n  overflow: auto;\n}\n\n.tree-header {\n  padding: 10px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.tree-actions {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 8px;\n}\n\n.module-detail {\n  margin-top: 10px;\n}\n\n.empty-tip {\n  padding: 40px 0;\n  text-align: center;\n}\n\n.custom-tree-node {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  font-size: 14px;\n  padding-right: 8px;\n}\n\n.card-title {\n  font-size: 15px;\n  font-weight: bold;\n}\n\n.import-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n  padding: 10px;\n}\n\n.parent-module-selector {\n  margin-bottom: 15px;\n  padding: 10px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n</style> "]}]}