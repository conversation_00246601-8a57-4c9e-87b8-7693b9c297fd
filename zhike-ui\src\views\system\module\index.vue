<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" class="mb8">
      <el-form-item label="模块名称" prop="moduleName">
        <el-input
          v-model="queryParams.moduleName"
          placeholder="请输入模块名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="模块编码" prop="moduleCode">
        <el-input
          v-model="queryParams.moduleCode"
          placeholder="请输入模块编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="业务类型" prop="businessType">
        <el-select v-model="queryParams.businessType" placeholder="请选择业务类型" clearable>
          <el-option
            v-for="dict in dict.type.product_plan_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属厂商" prop="competitorId">
        <el-select v-model="queryParams.competitorId" placeholder="请选择厂商" clearable filterable>
          <el-option
            v-for="item in competitorOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="模块类型" prop="moduleType">
        <el-select v-model="queryParams.moduleType" placeholder="模块类型" clearable>
          <el-option
            v-for="dict in dict.type.sys_module_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="模块状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="模块性质" prop="nature">
        <el-select v-model="queryParams.nature" placeholder="请选择模块性质" clearable>
          <el-option
            v-for="dict in dict.type.sys_module_nature"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="报价类型" prop="quotationNature">
        <el-select v-model="queryParams.quotationNature" placeholder="请选择报价类型" clearable>
          <el-option
            v-for="dict in dict.type.sys_quotation_nature"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否BD" prop="isBd">
        <el-select v-model="queryParams.isBd" placeholder="请选择是否BD" clearable>
          <el-option
            v-for="dict in dict.type.sys_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="医院评级" prop="hospitalCertification">
        <el-select v-model="queryParams.hospitalCertification" placeholder="请选择医院评级" clearable>
          <el-option
            v-for="dict in dict.type.hospital_certification"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="价格范围" prop="priceRange">
        <el-input-number
          v-model="queryParams.minPrice"
          placeholder="最小价格"
          :min="0"
          :precision="2"
          style="width: 120px"
        />
        <span style="margin: 0 10px">-</span>
        <el-input-number
          v-model="queryParams.maxPrice"
          placeholder="最大价格"
          :min="0"
          :precision="2"
          style="width: 120px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="20">
      <!-- 左侧树形结构 -->
      <el-col :span="6" class="tree-container">
        <div class="tree-header">
          <el-input
            v-model="filterText"
            placeholder="请输入关键字进行过滤"
            clearable
            prefix-icon="el-icon-search"
            size="small"
          />
          <div class="tree-actions">
            <el-button
              type="text"
              icon="el-icon-sort"
              size="mini"
              @click="toggleExpandAll"
            >{{ isExpandAll ? '全部折叠' : '全部展开' }}</el-button>
            <el-button
              type="text"
              icon="el-icon-refresh"
              size="mini"
              @click="refreshTree"
            >刷新树</el-button>
            <el-button
              type="text"
              icon="el-icon-search"
              size="mini"
              @click="showSearch = !showSearch"
            >{{ showSearch ? '收起搜索' : '高级搜索' }}</el-button>
          </div>
        </div>
        <el-tree
          ref="tree"
          :data="moduleList"
          :props="defaultProps"
          :filter-node-method="filterNode"
          :expand-on-click-node="false"
          node-key="moduleId"
          :default-expand-all="isExpandAll"
          highlight-current
          @node-click="handleNodeClick"
        >
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <span>
              <i class="el-icon-folder" style="margin-right: 5px"></i>
              {{ node.label }}
            </span>
          </span>
        </el-tree>
      </el-col>

      <!-- 右侧内容区域 -->
      <el-col :span="18">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span class="card-title">{{ currentModule.moduleName || '请选择模块' }}</span>
            <el-button-group style="float: right">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:module:add']"
        >新增</el-button>
        <el-button
          type="warning"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['system:module:add']"
        >导入模块</el-button>
              <el-button
                type="success"
                plain
                icon="el-icon-edit"
                size="mini"
                @click="handleUpdate(currentModule)"
                v-hasPermi="['system:module:edit']"
                v-if="currentModule.moduleId"
              >修改</el-button>
              <el-button
                type="danger"
                plain
                icon="el-icon-delete"
                size="mini"
                @click="handleDelete(currentModule)"
                v-hasPermi="['system:module:remove']"
                v-if="currentModule.moduleId"
              >删除</el-button>
        <el-button
          type="info"
          plain
                icon="el-icon-plus"
          size="mini"
                @click="handleAdd(currentModule)"
                v-hasPermi="['system:module:add']"
                v-if="currentModule.moduleId"
              >添加子模块</el-button>
            </el-button-group>
          </div>

          <!-- 模块详情内容 -->
          <div v-if="currentModule.moduleId" class="module-detail">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="模块名称">{{ currentModule.moduleName }}</el-descriptions-item>
              <el-descriptions-item label="模块编码">{{ currentModule.moduleCode }}</el-descriptions-item>
              <el-descriptions-item label="显示顺序">{{ currentModule.orderNum }}</el-descriptions-item>
              <el-descriptions-item label="上级模块">{{ getParentModuleName(currentModule.parentId) }}</el-descriptions-item>
              <el-descriptions-item label="模块类型">
                <dict-tag :options="dict.type.sys_module_type" :value="currentModule.moduleType"/>
              </el-descriptions-item>
              <el-descriptions-item label="模块属性">
                <dict-tag :options="dict.type.sys_module_property" :value="currentModule.moduleProperty"/>
              </el-descriptions-item>
              <el-descriptions-item label="所属厂商">{{ currentModule.competitorName }}</el-descriptions-item>
              <el-descriptions-item label="模块状态">
                <dict-tag :options="dict.type.sys_normal_disable" :value="currentModule.status"/>
              </el-descriptions-item>
              <el-descriptions-item label="性质">
                <dict-tag :options="dict.type.sys_module_nature" :value="currentModule.nature"/>
              </el-descriptions-item>
              <el-descriptions-item label="报价性质">
                <dict-tag :options="dict.type.sys_quotation_nature" :value="currentModule.quotationNature"/>
              </el-descriptions-item>
              <el-descriptions-item label="是否BD">
                <dict-tag :options="dict.type.sys_yes_no" :value="currentModule.isBd"/>
              </el-descriptions-item>
              <el-descriptions-item label="医院评级">
                <dict-tag :options="dict.type.hospital_certification" :value="currentModule.hospitalCertification"/>
              </el-descriptions-item>
              <el-descriptions-item label="模块说明">{{ currentModule.moduleDesc || '-' }}</el-descriptions-item>
              <el-descriptions-item label="业务类型">
                <dict-tag :options="dict.type.product_plan_type" :value="currentModule.businessType"/>
              </el-descriptions-item>
              <el-descriptions-item label="价格">
                <span v-if="currentModule.price">¥{{ currentModule.price }}</span>
                <span v-else>-</span>
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">{{ parseTime(currentModule.createTime) }}</el-descriptions-item>
              <el-descriptions-item label="更新时间">{{ parseTime(currentModule.updateTime) }}</el-descriptions-item>
              <el-descriptions-item label="备注" :span="2">{{ currentModule.remark }}</el-descriptions-item>
            </el-descriptions>
          </div>
          <div v-else class="empty-tip">
            <el-empty description="请从左侧选择模块"></el-empty>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 添加或修改系统模块对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="上级模块" prop="parentId">
              <treeselect 
                v-model="form.parentId" 
                :options="moduleOptions" 
                :normalizer="normalizer" 
                placeholder="选择上级模块"
                :disabled="form.parentId === 0" 
                :clearable="false"
                :default-expand-level="1"
                :show-count="true"
                noOptionsMessage="无"
                noResultsText="无"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="模块名称" prop="moduleName">
              <el-input v-model="form.moduleName" placeholder="请输入模块名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="模块编码" prop="moduleCode">
              <el-input v-model="form.moduleCode" placeholder="请输入模块编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="orderNum">
              <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="模块类型" prop="moduleType">
              <el-select v-model="form.moduleType" placeholder="请选择">
                <el-option
                  v-for="dict in dict.type.sys_module_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="模块属性" prop="moduleProperty">
              <el-select v-model="form.moduleProperty" placeholder="请选择">
                <el-option
                  v-for="dict in dict.type.sys_module_property"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属厂商" prop="competitorId">
              <el-select
                v-model="form.competitorId"
                placeholder="请选择厂商"
                filterable
                clearable
              >
                <el-option
                  v-for="item in competitorOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="模块状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性质" prop="nature">
              <el-select v-model="form.nature" placeholder="请选择">
                <el-option
                  v-for="dict in dict.type.sys_module_nature"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="报价性质" prop="quotationNature">
              <el-select v-model="form.quotationNature" placeholder="请选择">
                <el-option
                  v-for="dict in dict.type.sys_quotation_nature"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否BD" prop="isBd">
              <el-select v-model="form.isBd" placeholder="请选择">
                <el-option
                  v-for="dict in dict.type.sys_yes_no"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
                     <el-col :span="12">
             <el-form-item label="医院评级" prop="hospitalCertification">
               <el-select v-model="form.hospitalCertification" placeholder="请选择">
                 <el-option
                   v-for="dict in dict.type.hospital_certification"
                   :key="dict.value"
                   :label="dict.label"
                   :value="dict.value"
                 ></el-option>
               </el-select>
             </el-form-item>
           </el-col>
           <el-col :span="12">
             <el-form-item label="价格" prop="price">
               <el-input-number
                 v-model="form.price"
                 placeholder="请输入价格"
                 :min="0"
                 :precision="2"
                 controls-position="right"
                 style="width: 100%"
               />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="业务类型" prop="businessType">
              <el-select v-model="form.businessType" placeholder="请选择业务类型">
                <el-option
                  v-for="dict in dict.type.product_plan_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="模块说明" prop="moduleDesc">
              <el-input v-model="form.moduleDesc" type="textarea" :rows="3" placeholder="请输入模块说明" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="formLoading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 模块导入对话框 -->
    <el-dialog title="模块导入" :visible.sync="importOpen" width="800px" append-to-body>
      <div v-if="!parsedModules.length">
        <el-upload
          ref="upload"
          action="#"
          :limit="1"
          accept=".html"
          :on-exceed="handleExceed"
          :before-upload="beforeUpload"
          :auto-upload="false"
          :file-list="fileList"
          drag
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将HTML文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">只能上传HTML文件，且不超过10MB</div>
        </el-upload>
        <div style="margin-top: 20px; text-align: center">
          <el-button type="primary" @click="handleParseFile" :loading="parseLoading">解析文件</el-button>
        </div>
      </div>

      <div v-else>
        <div class="import-header">
          <span>解析结果：共找到 {{ parsedModules.length }} 个模块</span>
          <el-button type="text" @click="resetImport">重新选择文件</el-button>
        </div>

        <!-- 父模块选择 -->
        <div class="parent-module-selector">
          <el-form :inline="true">
            <el-form-item label="导入到父模块：">
              <treeselect
                v-model="selectedParentModuleId"
                :options="moduleOptions"
                :normalizer="normalizer"
                :show-count="true"
                placeholder="选择父模块（可选）"
                :clearable="true"
                style="width: 300px"
              />
            </el-form-item>
          </el-form>
        </div>

        <el-table
          ref="importTable"
          :data="parsedModules"
          border
          style="width: 100%; margin-top: 10px"
          max-height="400"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" :selectable="checkSelectable" />
          <el-table-column prop="moduleName" label="模块名称" min-width="200">
            <template slot-scope="scope">
              <span
                :style="{
                  'margin-left': (scope.row.level - 1) * 20 + 'px',
                  'font-weight': scope.row.isParent ? 'bold' : 'normal',
                  'color': scope.row.isParent ? '#409EFF' : '#606266'
                }"
              >
                <i v-if="scope.row.isParent" class="el-icon-folder" style="margin-right: 5px"></i>
                <i v-else class="el-icon-document" style="margin-right: 5px"></i>
                {{ scope.row.moduleName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="moduleCode" label="模块编码" width="150" />
          <el-table-column prop="parentName" label="父模块" width="150">
            <template slot-scope="scope">
              {{ scope.row.parentName || '无' }}
            </template>
          </el-table-column>
          <el-table-column prop="level" label="层级" width="60">
            <template slot-scope="scope">
              <el-tag :type="scope.row.level === 1 ? 'primary' : scope.row.level === 2 ? 'success' : 'warning'" size="mini">
                {{ scope.row.level || 1 }}级
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="orderNum" label="排序" width="80" />
        </el-table>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button v-if="parsedModules.length" type="primary" @click="handleConfirmImport" :loading="importLoading">
          确认导入
        </el-button>
        <el-button @click="cancelImport">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listModule, getModule, delModule, addModule, updateModule, parseHtmlFile, batchImportModules } from "@/api/system/module";
import { listCompetitor } from "@/api/competitor/competitor";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { pinyin } from 'pinyin-pro';

// 添加汉字转拼音的工具函数
function convertToPinyin(chinese) {
  if (!chinese) return '';
  console.log('输入的中文：', chinese);
  
  // 将汉字转换为拼音，并转换为大写
  const pinyinResult = pinyin(chinese, {
    toneType: 'none',    // 不带声调
    type: 'array',       // 返回拼音数组
    nonZh: 'consecutive', // 非汉字连续返回
    pattern: 'first'     // 只取首字母
  });
  console.log('pinyin转换结果：', pinyinResult);
  
  const upperResult = pinyinResult.map(py => py.toUpperCase());
  console.log('转大写结果：', upperResult);
  
  const finalResult = upperResult.join('');
  console.log('最终结果：', finalResult);
  
  return finalResult;
}

export default {
  name: "Module",
  dicts: ['sys_normal_disable', 'sys_module_type', 'sys_module_property', 'sys_module_nature', 'sys_quotation_nature', 'sys_yes_no', 'hospital_certification', 'product_plan_type'],
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 表单加载状态
      formLoading: false,
      // 表格树数据
      moduleList: [],
      // 模块树选项
      moduleOptions: [],
      // 厂商选项
      competitorOptions: [],
      // 默认卫宁厂商ID
      defaultCompetitorId: null,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部展开
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 查询参数
      queryParams: {
        moduleName: undefined,
        moduleCode: undefined,
        moduleType: undefined,
        status: undefined,
        competitorId: undefined,
        nature: undefined,
        quotationNature: undefined,
        isBd: undefined,
        hospitalCertification: undefined,
        minPrice: undefined,
        maxPrice: undefined,
        businessType: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        moduleName: [
          { required: true, message: "模块名称不能为空", trigger: "blur" }
        ],
        orderNum: [
          { required: true, message: "显示排序不能为空", trigger: "blur" }
        ],
        moduleType: [
          { required: true, message: "模块类型不能为空", trigger: "blur" }
        ],
        moduleProperty: [
          { required: true, message: "模块属性不能为空", trigger: "blur" }
        ],
        competitorId: [
          { required: true, message: "请选择所属厂商", trigger: "change" }
        ],
        businessType: [
          { required: true, message: "请选择业务类型", trigger: "change" }
        ]
      },
      // 树形结构相关
      filterText: '',
      defaultProps: {
        children: 'children',
        label: 'moduleName'
      },
      currentModule: {}, // 当前选中的模块
      showSearch: false, // 默认不显示搜索条件
      expandedKeys: [], // 当前展开的节点ID列表
      // 导入相关
      importOpen: false, // 导入对话框是否显示
      parseLoading: false, // 解析加载状态
      importLoading: false, // 导入加载状态
      fileList: [], // 上传文件列表
      parsedModules: [], // 解析出的模块数据
      selectedModules: [], // 选中的模块数据
      selectedParentModuleId: null, // 选择的父模块ID
    };
  },
  watch: {
    // 监听模块名称变化，自动生成模块编码
    'form.moduleName': {
      handler(newVal) {
        // 在新增或编辑时都自动生成编码
        if (newVal) {
          console.log('模块名称变化：', newVal);
          this.form.moduleCode = convertToPinyin(newVal);
          console.log('生成的模块编码：', this.form.moduleCode);
        }
      }
    },
    // 监听上级模块变化，重新获取显示排序
    'form.parentId': {
      handler(newVal) {
        // 如果是新增模式（没有moduleId）并且选择了有效的上级模块
        if (!this.form.moduleId && newVal !== undefined) {
          this.getMaxOrderNum(newVal);
        }
      }
    },
    // 监听过滤文本变化
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  created() {
    this.getList();
    this.getCompetitorOptions();
  },
  methods: {
    /** 查询模块列表 */
    getList() {
      this.loading = true;
      
      // 保存当前展开的节点ID
      this.saveExpandedKeys();
      
      // 确保将厂商ID传递给后端接口
      let params = Object.assign({}, this.queryParams);
      
      listModule(params).then(response => {
        this.moduleList = this.handleTree(response.data, "moduleId");
        this.loading = false;
        
        // 恢复展开的节点
        this.$nextTick(() => {
          this.restoreExpandedKeys();
        });
      });
    },
    /** 查询厂商选项 */
    getCompetitorOptions() {
      listCompetitor({ status: '0', pageSize: 9999 }).then(response => {
        this.competitorOptions = response.rows;
      });
    },
    /** 转换模块数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.moduleId,
        label: node.moduleName,
        children: node.children
      };
    },
    // 过滤节点
    filterNode(value, data) {
      if (!value) return true;
      return data.moduleName.toLowerCase().indexOf(value.toLowerCase()) !== -1;
    },
    // 节点点击事件
    handleNodeClick(data) {
      this.currentModule = data;
    },
    // 获取父模块名称
    getParentModuleName(parentId) {
      if (!parentId || parentId === 0) {
        return '无';
      }
      
      // 递归在模块树中查找指定ID的模块
      const findModule = (list, id) => {
        for (const item of list) {
          if (item.moduleId === id) {
            return item.moduleName;
          }
          if (item.children && item.children.length > 0) {
            const found = findModule(item.children, id);
            if (found) return found;
          }
        }
        return null;
      };
      
      const name = findModule(this.moduleList, parentId);
      return name || '未知';
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        moduleId: undefined,
        parentId: undefined,
        moduleName: undefined,
        moduleCode: undefined,
        orderNum: 99, // 设置默认排序为99
        moduleType: "1",  // 修改默认值为系统模块
        moduleProperty: "3", // 默认为功能
        competitorId: undefined, // 取消默认厂商设置
        status: "0",
        nature: undefined,
        quotationNature: undefined,
        isBd: undefined,
        hospitalCertification: undefined,
        price: undefined,
        remark: undefined,
        moduleDesc: undefined,
        businessType: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      if (row && row.moduleId) {
        this.form.parentId = row.moduleId;
        // 如果是子模块，继承父模块的厂商ID
        if (row.competitorId) {
          this.form.competitorId = row.competitorId;
        }
        
        // 获取同级模块的最大排序号
        this.getMaxOrderNum(row.moduleId);
      } else {
        // 获取顶级模块的最大排序号
        this.getMaxOrderNum(0);
      }
      this.open = true;
      this.title = "添加模块";
      listModule().then(response => {
        this.moduleOptions = this.handleTree(response.data, "moduleId");
      });
    },
    /** 获取同级模块中的最大排序号 */
    getMaxOrderNum(parentId) {
      // 明确查询同一父模块下的直接子模块
      listModule({ parentId: parentId }).then(response => {
        if (response.data && response.data.length > 0) {
          // 过滤出具有相同parentId的模块，确保只处理真正的同级模块
          const siblingModules = response.data.filter(item => item.parentId === parentId);
          
          if (siblingModules.length > 0) {
            // 找出同级模块中的最大排序号
            const maxOrderNum = Math.max(...siblingModules.map(m => m.orderNum || 0));
            // 设置新模块的排序号为最大值+1
            this.form.orderNum = maxOrderNum + 1;
            console.log('获取到同级模块最大排序号：', maxOrderNum, '设置新模块排序号为：', this.form.orderNum);
          } else {
            // 如果该父模块下没有子模块，则设置排序号为1
            this.form.orderNum = 1;
            console.log('该父模块下无子模块，设置新模块排序号为：1');
          }
        } else {
          // 如果没有返回数据，则设置排序号为1
          this.form.orderNum = 1;
          console.log('查询无返回数据，设置新模块排序号为：1');
        }
      }).catch(error => {
        console.error('获取同级模块排序号失败：', error);
        // 发生错误时设置默认排序号
        this.form.orderNum = 1;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      if (!row || !row.moduleId) {
        this.$modal.msgError("请先选择要修改的模块");
        return;
      }
      
      getModule(row.moduleId).then(response => {
        this.form = response.data;
        // 如果是顶级模块(parentId=0),则禁用上级模块选择
        if (this.form.parentId === 0) {
          this.$nextTick(() => {
            this.$refs.form.clearValidate('parentId');
            this.$set(this.rules, 'parentId', []);
          });
          // 添加一个顶级节点选项
          this.moduleOptions = [{
            id: 0,
            label: '无'
          }];
        } else {
          listModule().then(response => {
            this.moduleOptions = this.handleTree(response.data, "moduleId");
          });
        }
        this.open = true;
        this.title = "修改模块";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.formLoading = true;
          
          // 保存当前展开的节点ID
          this.saveExpandedKeys();
          
          // 记录当前表单的父节点ID和本身ID
          const parentId = this.form.parentId;
          const moduleId = this.form.moduleId;
          
          if (moduleId != undefined) {
            updateModule(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              
              // 将当前节点ID添加到展开节点列表
              if (!this.expandedKeys.includes(moduleId) && moduleId !== 0) {
                this.expandedKeys.push(moduleId);
              }
              
              // 将父节点ID添加到展开节点列表
              if (parentId && !this.expandedKeys.includes(parentId) && parentId !== 0) {
                this.expandedKeys.push(parentId);
              }
              
              this.getList();
              
              // 如果是当前模块，也要刷新详情
              if (this.currentModule.moduleId === moduleId) {
                this.refreshModuleDetail(moduleId);
              }
              
              this.formLoading = false;
            }).catch(() => {
              this.formLoading = false;
            });
          } else {
            addModule(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              
              // 确保父节点在展开列表中
              if (parentId && !this.expandedKeys.includes(parentId) && parentId !== 0) {
                this.expandedKeys.push(parentId);
              }
              
              // 如果是新增的根节点，记录其ID
              if (response.data && response.data.moduleId && parentId === 0) {
                const newModuleId = response.data.moduleId;
                this.expandedKeys.push(newModuleId);
              }
              
              this.getList();
              this.formLoading = false;
            }).catch(() => {
              this.formLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      if (!row || !row.moduleId) {
        this.$modal.msgError("未选择要删除的模块");
        return;
      }
      
      // 检查是否有子节点
      if (row.children && row.children.length > 0) {
        this.$modal.msgError('该模块下存在子模块，请先删除子模块');
        return;
      }
      
      this.$modal.confirm('确定要删除模块【' + row.moduleName + '】吗？删除后无法恢复！').then(() => {
        return delModule(row.moduleId);
      }).then(() => {
        // 记录当前删除的模块ID
        const deletedModuleId = row.moduleId;
        
        // 刷新模块列表
        this.getList();
        
        // 如果右侧当前显示的就是被删除的模块，则清空右侧内容
        if (this.currentModule && this.currentModule.moduleId === deletedModuleId) {
          this.currentModule = {};
        }
        
        this.$modal.msgSuccess("模块【" + row.moduleName + "】删除成功");
      }).catch(() => {});
    },
    // 刷新模块详情
    refreshModuleDetail(moduleId) {
      if (!moduleId) return;
      
      // 获取最新的模块详情数据
      getModule(moduleId).then(response => {
        if (response.data) {
          // 更新当前模块对象
          this.currentModule = response.data;
        }
      }).catch(error => {
        console.error("获取模块详情失败", error);
      });
    },
    // 切换全部展开/折叠
    toggleExpandAll() {
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        if (this.isExpandAll) {
          this.$refs.tree.expandAll();
        } else {
          this.$refs.tree.collapseAll();
        }
        
        // 更新展开节点状态
        this.saveExpandedKeys();
        
        // 显示成功提示
        this.$message({
          message: this.isExpandAll ? '已全部展开' : '已全部折叠',
          type: 'success',
          duration: 1500
        });
      });
    },
    /** 刷新树结构 */
    refreshTree() {
      console.log('刷新模块树结构');
      // 显示加载中提示
      this.$modal.loading("正在刷新树结构，请稍候...");
      
      // 重新获取模块数据
      listModule(this.queryParams).then(response => {
        // 处理树形结构数据
        this.moduleList = this.handleTree(response.data, "moduleId");
        this.loading = false;
        
        // 关闭加载提示并显示成功消息
        this.$modal.closeLoading();
        this.$modal.msgSuccess("树结构刷新成功");
        
        // 保持树的展开状态 - 放在成功提示之后延时执行，避免卡顿
        setTimeout(() => {
          if (this.$refs.tree) {
            // 设置默认展开状态
            this.$refs.tree.store.defaultExpandAll = this.isExpandAll;
            
            // 如果当前有选中的模块，刷新其详情
            if (this.currentModule && this.currentModule.moduleId) {
              this.refreshModuleDetail(this.currentModule.moduleId);
            }
          }
        }, 100);
      }).catch(error => {
        console.error("刷新模块树结构失败:", error);
        this.$modal.closeLoading();
        this.$modal.msgError("刷新树结构失败");
      });
    },
    // 保存当前所有展开节点的ID
    saveExpandedKeys() {
      if (this.$refs.tree) {
        const expandedKeys = [];
        
        // 遍历所有节点，记录已展开的节点ID
        const nodes = this.$refs.tree.store.nodesMap;
        for (const key in nodes) {
          if (nodes.hasOwnProperty(key) && nodes[key].expanded) {
            expandedKeys.push(Number(key));
          }
        }
        
        this.expandedKeys = expandedKeys;
        console.log('保存展开节点状态:', this.expandedKeys);
      }
    },
    // 恢复节点展开状态
    restoreExpandedKeys() {
      if (this.$refs.tree) {
        // 如果是展开全部的状态，全部展开
        if (this.isExpandAll) {
          this.$refs.tree.expandAll();
          return;
        }
        
        // 如果有记录展开节点，则恢复这些节点的展开状态
        if (this.expandedKeys && this.expandedKeys.length > 0) {
          console.log('恢复展开节点状态:', this.expandedKeys);
          const nodes = this.$refs.tree.store.nodesMap;
          
          // 模拟点击每个保存的展开节点，触发展开
          this.expandedKeys.forEach(key => {
            if (nodes[key]) {
              nodes[key].expanded = true;
            }
          });
          
          // 强制更新树视图
          this.$refs.tree.store._getAllNodes().forEach(node => {
            this.$refs.tree.store._setExpandedKeys(node);
          });
        }
      }
    },
    // 导入模块
    handleImport() {
      this.importOpen = true;
      this.resetImport();
      this.loadParentModuleOptions();
    },
    // 重置导入状态
    resetImport() {
      this.fileList = [];
      this.parsedModules = [];
      this.selectedModules = [];
      this.selectedParentModuleId = null;
      this.parseLoading = false;
      this.importLoading = false;
      this.$refs.upload && this.$refs.upload.clearFiles();
    },
    // 文件数量超出限制
    handleExceed() {
      this.$modal.msgWarning('只能选择一个HTML文件');
    },
    // 上传前检查
    beforeUpload(file) {
      const isHTML = file.type === 'text/html' || file.name.toLowerCase().endsWith('.html');
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isHTML) {
        this.$modal.msgError('只能上传HTML文件!');
        return false;
      }
      if (!isLt10M) {
        this.$modal.msgError('上传文件大小不能超过 10MB!');
        return false;
      }
      return false; // 阻止自动上传
    },
    // 解析文件
    handleParseFile() {
      const files = this.$refs.upload.uploadFiles;
      if (!files || files.length === 0) {
        this.$modal.msgWarning('请先选择HTML文件');
        return;
      }

      this.parseLoading = true;
      const file = files[0].raw;

      parseHtmlFile(file).then(response => {
        if (response.code === 200) {
          this.parsedModules = response.data;
          this.$modal.msgSuccess('文件解析成功，共解析出 ' + this.parsedModules.length + ' 个模块');
        } else {
          this.$modal.msgError(response.msg || '解析失败');
        }
      }).catch(error => {
        console.error('解析文件失败:', error);
        this.$modal.msgError('解析文件失败');
      }).finally(() => {
        this.parseLoading = false;
      });
    },
    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedModules = selection;
    },
    // 检查行是否可选择
    checkSelectable(row, index) {
      return true; // 所有行都可选择
    },
    // 确认导入
    handleConfirmImport() {
      if (!this.parsedModules || this.parsedModules.length === 0) {
        this.$modal.msgWarning('没有可导入的模块数据');
        return;
      }

      // 获取选中的模块
      const selectedModules = this.selectedModules && this.selectedModules.length > 0 ?
        this.selectedModules : this.parsedModules;

      if (!selectedModules || selectedModules.length === 0) {
        this.$modal.msgWarning('请选择要导入的模块');
        return;
      }

      this.$modal.confirm('确定要导入选中的 ' + selectedModules.length + ' 个模块吗？').then(() => {
        this.importLoading = true;

        // 构建导入参数
        const importParams = {
          modules: selectedModules,
          parentModuleId: this.selectedParentModuleId
        };

        batchImportModules(importParams).then(response => {
          if (response.code === 200) {
            this.$modal.msgSuccess(response.msg || '导入成功');
            this.importOpen = false;
            this.getList(); // 刷新模块列表
          } else {
            this.$modal.msgError(response.msg || '导入失败');
          }
        }).catch(error => {
          console.error('导入模块失败:', error);
          this.$modal.msgError('导入模块失败');
        }).finally(() => {
          this.importLoading = false;
        });
      });
    },
    // 取消导入
    cancelImport() {
      this.importOpen = false;
      this.resetImport();
    },
    // 加载父模块选项
    loadParentModuleOptions() {
      // 获取所有模块，过滤出模块属性为"目录"（值为1）的模块作为可选父模块
      listModule({ moduleProperty: '1', status: '0' }).then(response => {
        if (response.code === 200 && response.data) {
          // 构建树形结构
          this.moduleOptions = this.handleTree(response.data, "moduleId");
          console.log('加载父模块选项:', this.moduleOptions);
        } else {
          this.moduleOptions = [];
        }
      }).catch(error => {
        console.error('加载父模块选项失败:', error);
        this.moduleOptions = [];
      });
    }
  }
};
</script> 

<style scoped>
.mb8 {
  margin-bottom: 8px;
}

.tree-container {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: calc(100vh - 130px);
  overflow: auto;
}

.tree-header {
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
}

.tree-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
}

.module-detail {
  margin-top: 10px;
}

.empty-tip {
  padding: 40px 0;
  text-align: center;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.card-title {
  font-size: 15px;
  font-weight: bold;
}

.import-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 10px;
}

.parent-module-selector {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}
</style> 