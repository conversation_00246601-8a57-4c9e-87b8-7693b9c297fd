{"remainingRequest": "D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\ruoyi\\Zhike\\zhike-ui\\src\\router\\modules\\kms.js", "dependencies": [{"path": "D:\\ruoyi\\Zhike\\zhike-ui\\src\\router\\modules\\kms.js", "mtime": 1754196499026}, {"path": "D:\\ruoyi\\Zhike\\zhike-ui\\babel.config.js", "mtime": 1737337534038}, {"path": "D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737340298796}, {"path": "D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737340300972}, {"path": "D:\\ruoyi\\Zhike\\zhike-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1737340299331}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_layout", "_interopRequireDefault", "require", "_default", "exports", "default", "path", "component", "Layout", "redirect", "name", "meta", "title", "icon", "children", "resolve", "permissions", "hidden"], "sources": ["D:/ruoyi/Zhike/zhike-ui/src/router/modules/kms.js"], "sourcesContent": ["import Layout from '@/layout'\r\n\r\n// KMS知识管理系统路由\r\nexport default {\r\n  path: '/kms',\r\n  component: Layout,\r\n  redirect: 'noredirect',\r\n  name: '<PERSON><PERSON>',\r\n  meta: { title: '知识管理', icon: 'documentation' },\r\n  children: [\r\n    {\r\n      path: 'catalog',\r\n      component: (resolve) => require(['@/views/kms/kms_catalog/index'], resolve),\r\n      name: 'KmsCatalog',\r\n      meta: { title: '知识目录', icon: 'tree' }\r\n    },\r\n    {\r\n      path: 'browse',\r\n      component: (resolve) => require(['@/views/kms/kms_browse/index'], resolve),\r\n      name: 'KmsBrowse',\r\n      meta: { title: '知识浏览', icon: 'search' }\r\n    },\r\n    {\r\n      path: 'main',\r\n      component: (resolve) => require(['@/views/kms/kms_main/index'], resolve),\r\n      name: 'KmsMain',\r\n      meta: { title: '知识管理', icon: 'guide' }\r\n    },\r\n    {\r\n      path: 'file',\r\n      component: (resolve) => require(['@/views/kms/kms_file/index'], resolve),\r\n      name: 'KmsFile',\r\n      meta: { title: '文件管理', icon: 'zip' }\r\n    },\r\n    {\r\n      path: 'material',\r\n      component: (resolve) => require(['@/views/kms/material/index'], resolve),\r\n      name: 'KmsMaterial',\r\n      meta: { title: '方案素材', icon: 'form' }\r\n    },\r\n    {\r\n      path: 'materialSearch',\r\n      component: (resolve) => require(['@/views/kms/material/search'], resolve),\r\n      name: 'MaterialSearch',\r\n      meta: { title: '素材搜索', icon: 'search' }\r\n    },\r\n    {\r\n      path: 'material-tag',\r\n      component: (resolve) => require(['@/views/kms/material/tag/index'], resolve),\r\n      name: 'MaterialTag',\r\n      meta: { title: '标签管理', icon: 'collection-tag', permissions: ['kms:tag:manage'] }\r\n    },\r\n    {\r\n      path: 'word-parse',\r\n      component: (resolve) => require(['@/views/kms/material/word-parse/index'], resolve),\r\n      name: 'WordParse',\r\n      meta: { title: 'Word文档解析', icon: 'upload', hidden: true }\r\n    },\r\n    {\r\n      path: 'indexStatus',\r\n      component: (resolve) => require(['@/views/kms/materialSearch/indexStatus'], resolve),\r\n      name: 'IndexStatus',\r\n      meta: { title: '索引管理', icon: 'database', permissions: ['kms:material:edit'] }\r\n    },\r\n    {\r\n      path: 'video-manage',\r\n      component: (resolve) => require(['@/views/kms/video/index'], resolve),\r\n      name: 'CmsVideo',\r\n      meta: { title: '视频管理', icon: 'video' }\r\n    },\r\n    {\r\n      path: 'video-category',\r\n      component: (resolve) => require(['@/views/kms/video/category'], resolve),\r\n      name: 'CmsVideoCategory',\r\n      meta: { title: '视频分类', icon: 'nested', permissions: ['kms:video:list'] }\r\n    }\r\n  ]\r\n} "], "mappings": ";;;;;;;AAAA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GACe;EACbC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEC,eAAM;EACjBC,QAAQ,EAAE,YAAY;EACtBC,IAAI,EAAE,KAAK;EACXC,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE;EAAgB,CAAC;EAC9CC,QAAQ,EAAE,CACR;IACER,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAXA,SAASA,CAAGQ,OAAO;MAAA,OAAKb,OAAO,CAAC,CAAC,+BAA+B,CAAC,EAAEa,OAAO,CAAC;IAAA;IAC3EL,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAO;EACtC,CAAC,EACD;IACEP,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,SAAXA,SAASA,CAAGQ,OAAO;MAAA,OAAKb,OAAO,CAAC,CAAC,8BAA8B,CAAC,EAAEa,OAAO,CAAC;IAAA;IAC1EL,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAS;EACxC,CAAC,EACD;IACEP,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,SAAXA,SAASA,CAAGQ,OAAO;MAAA,OAAKb,OAAO,CAAC,CAAC,4BAA4B,CAAC,EAAEa,OAAO,CAAC;IAAA;IACxEL,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAQ;EACvC,CAAC,EACD;IACEP,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,SAAXA,SAASA,CAAGQ,OAAO;MAAA,OAAKb,OAAO,CAAC,CAAC,4BAA4B,CAAC,EAAEa,OAAO,CAAC;IAAA;IACxEL,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAM;EACrC,CAAC,EACD;IACEP,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAE,SAAXA,SAASA,CAAGQ,OAAO;MAAA,OAAKb,OAAO,CAAC,CAAC,4BAA4B,CAAC,EAAEa,OAAO,CAAC;IAAA;IACxEL,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAO;EACtC,CAAC,EACD;IACEP,IAAI,EAAE,gBAAgB;IACtBC,SAAS,EAAE,SAAXA,SAASA,CAAGQ,OAAO;MAAA,OAAKb,OAAO,CAAC,CAAC,6BAA6B,CAAC,EAAEa,OAAO,CAAC;IAAA;IACzEL,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAS;EACxC,CAAC,EACD;IACEP,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAE,SAAXA,SAASA,CAAGQ,OAAO;MAAA,OAAKb,OAAO,CAAC,CAAC,gCAAgC,CAAC,EAAEa,OAAO,CAAC;IAAA;IAC5EL,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,gBAAgB;MAAEG,WAAW,EAAE,CAAC,gBAAgB;IAAE;EACjF,CAAC,EACD;IACEV,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAE,SAAXA,SAASA,CAAGQ,OAAO;MAAA,OAAKb,OAAO,CAAC,CAAC,uCAAuC,CAAC,EAAEa,OAAO,CAAC;IAAA;IACnFL,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE;MAAEC,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE,QAAQ;MAAEI,MAAM,EAAE;IAAK;EAC1D,CAAC,EACD;IACEX,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAE,SAAXA,SAASA,CAAGQ,OAAO;MAAA,OAAKb,OAAO,CAAC,CAAC,wCAAwC,CAAC,EAAEa,OAAO,CAAC;IAAA;IACpFL,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,UAAU;MAAEG,WAAW,EAAE,CAAC,mBAAmB;IAAE;EAC9E,CAAC,EACD;IACEV,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAE,SAAXA,SAASA,CAAGQ,OAAO;MAAA,OAAKb,OAAO,CAAC,CAAC,yBAAyB,CAAC,EAAEa,OAAO,CAAC;IAAA;IACrEL,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAQ;EACvC,CAAC,EACD;IACEP,IAAI,EAAE,gBAAgB;IACtBC,SAAS,EAAE,SAAXA,SAASA,CAAGQ,OAAO;MAAA,OAAKb,OAAO,CAAC,CAAC,4BAA4B,CAAC,EAAEa,OAAO,CAAC;IAAA;IACxEL,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,QAAQ;MAAEG,WAAW,EAAE,CAAC,gBAAgB;IAAE;EACzE,CAAC;AAEL,CAAC", "ignoreList": []}]}