import request from '@/utils/request'

// 查询系统模块列表
export function listModule(query) {
    return request({
        url: '/system/module/list',
        method: 'get',
        params: query
    })
}

// 查询系统模块详细
export function getModule(moduleId) {
    return request({
        url: '/system/module/' + moduleId,
        method: 'get'
    })
}

// 新增系统模块
export function addModule(data) {
    return request({
        url: '/system/module',
        method: 'post',
        data: data
    })
}

// 修改系统模块
export function updateModule(data) {
    return request({
        url: '/system/module',
        method: 'put',
        data: data
    })
}

// 删除系统模块
export function delModule(moduleId) {
    return request({
        url: '/system/module/' + moduleId,
        method: 'delete'
    })
}

// 解析HTML文件中的模块数据
export function parseHtmlFile(file) {
    const formData = new FormData()
    formData.append('file', file)
    return request({
        url: '/system/module/parse-html',
        method: 'post',
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}

// 批量导入模块
export function batchImportModules(moduleData) {
    return request({
        url: '/system/module/batch-import',
        method: 'post',
        data: moduleData
    })
}