<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhike.system.mapper.SysModuleMapper">
    
    <resultMap type="SysModule" id="SysModuleResult">
        <id     property="moduleId"     column="module_id"     />
        <result property="parentId"     column="parent_id"   />
        <result property="ancestors"    column="ancestors"   />
        <result property="moduleName"   column="module_name"   />
        <result property="moduleCode"   column="module_code"   />
        <result property="orderNum"     column="order_num"   />
        <result property="moduleType"   column="module_type"   />
        <result property="moduleProperty" column="module_property" />
        <result property="competitorId" column="competitor_id" />
        <result property="competitorName" column="competitor_name" />
        <result property="status"       column="status"      />
        <result property="nature"       column="nature"      />
        <result property="quotationNature" column="quotation_nature" />
        <result property="isBd"         column="is_bd"       />
        <result property="hospitalCertification" column="hospital_certification" />
        <result property="price"        column="price"       />
        <result property="moduleDesc"   column="module_desc" />
        <result property="businessType" column="business_type" />
        <result property="delFlag"      column="del_flag"    />
        <result property="createBy"     column="create_by"   />
        <result property="createTime"   column="create_time" />
        <result property="updateBy"     column="update_by"   />
        <result property="updateTime"   column="update_time" />
        <result property="remark"       column="remark"      />
    </resultMap>
    
    <sql id="selectModuleVo">
        select m.module_id, m.parent_id, m.ancestors, m.module_name, m.module_code, m.order_num, 
               m.module_type, m.module_property, m.competitor_id, c.name as competitor_name, 
               m.status, m.nature, m.quotation_nature, m.is_bd, m.hospital_certification, m.price,
               m.module_desc, m.business_type,
               m.del_flag, m.create_by, m.create_time, m.update_by, m.update_time, m.remark
        from sys_module m
        left join com_competitor_info c on m.competitor_id = c.id
    </sql>
    
    <select id="selectModuleList" parameterType="SysModule" resultMap="SysModuleResult">
        <include refid="selectModuleVo"/>
        where m.del_flag = '0'
        <if test="moduleName != null and moduleName != ''">
            AND m.module_name like concat('%', #{moduleName}, '%')
        </if>
        <if test="moduleCode != null and moduleCode != ''">
            AND m.module_code = #{moduleCode}
        </if>
        <if test="status != null and status != ''">
            AND m.status = #{status}
        </if>
        <if test="moduleType != null and moduleType != ''">
            AND m.module_type = #{moduleType}
        </if>
        <if test="moduleProperty != null and moduleProperty != ''">
            AND m.module_property = #{moduleProperty}
        </if>
        <if test="competitorId != null">
            AND m.competitor_id = #{competitorId}
        </if>
        <if test="nature != null and nature != ''">
            AND m.nature = #{nature}
        </if>
        <if test="quotationNature != null and quotationNature != ''">
            AND m.quotation_nature = #{quotationNature}
        </if>
        <if test="isBd != null and isBd != ''">
            AND m.is_bd = #{isBd}
        </if>
        <if test="hospitalCertification != null and hospitalCertification != ''">
            AND m.hospital_certification = #{hospitalCertification}
        </if>
        <if test="businessType != null and businessType != ''">
            AND m.business_type = #{businessType}
        </if>
        <if test="minPrice != null">
            AND m.price >= #{minPrice}
        </if>
        <if test="maxPrice != null">
            AND m.price &lt;= #{maxPrice}
        </if>
        order by m.parent_id, m.order_num
    </select>
    
    <select id="selectModuleById" parameterType="Long" resultMap="SysModuleResult">
        <include refid="selectModuleVo"/>
        where m.module_id = #{moduleId}
    </select>
    
    <select id="hasChildByModuleId" parameterType="Long" resultType="Integer">
        select count(1) from sys_module
        where parent_id = #{moduleId} and del_flag = '0'
    </select>
    
    <insert id="insertModule" parameterType="SysModule" useGeneratedKeys="true" keyProperty="moduleId">
        insert into sys_module(
            <if test="moduleId != null">module_id,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="moduleName != null and moduleName != ''">module_name,</if>
            <if test="moduleCode != null">module_code,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="moduleType != null">module_type,</if>
            <if test="moduleProperty != null">module_property,</if>
            <if test="competitorId != null">competitor_id,</if>
            <if test="status != null">status,</if>
            <if test="nature != null">nature,</if>
            <if test="quotationNature != null">quotation_nature,</if>
            <if test="isBd != null">is_bd,</if>
            <if test="hospitalCertification != null">hospital_certification,</if>
            <if test="price != null">price,</if>
            <if test="moduleDesc != null">module_desc,</if>
            <if test="businessType != null">business_type,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        )values(
            <if test="moduleId != null">#{moduleId},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="moduleName != null and moduleName != ''">#{moduleName},</if>
            <if test="moduleCode != null">#{moduleCode},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="moduleType != null">#{moduleType},</if>
            <if test="moduleProperty != null">#{moduleProperty},</if>
            <if test="competitorId != null">#{competitorId},</if>
            <if test="status != null">#{status},</if>
            <if test="nature != null">#{nature},</if>
            <if test="quotationNature != null">#{quotationNature},</if>
            <if test="isBd != null">#{isBd},</if>
            <if test="hospitalCertification != null">#{hospitalCertification},</if>
            <if test="price != null">#{price},</if>
            <if test="moduleDesc != null">#{moduleDesc},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
        )
    </insert>
    
    <update id="updateModule" parameterType="SysModule">
        update sys_module
        <set>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="moduleName != null and moduleName != ''">module_name = #{moduleName},</if>
            <if test="moduleCode != null">module_code = #{moduleCode},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="moduleType != null">module_type = #{moduleType},</if>
            <if test="moduleProperty != null">module_property = #{moduleProperty},</if>
            <if test="competitorId != null">competitor_id = #{competitorId},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="nature != null">nature = #{nature},</if>
            <if test="quotationNature != null">quotation_nature = #{quotationNature},</if>
            <if test="isBd != null">is_bd = #{isBd},</if>
            <if test="hospitalCertification != null">hospital_certification = #{hospitalCertification},</if>
            <if test="price != null">price = #{price},</if>
            <if test="moduleDesc != null">module_desc = #{moduleDesc},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where module_id = #{moduleId}
    </update>
    
    <delete id="deleteModuleById" parameterType="Long">
        update sys_module set del_flag = '2' where module_id = #{moduleId}
    </delete>
    
    <delete id="deleteModuleByIds" parameterType="Long">
        update sys_module set del_flag = '2' where module_id in
        <foreach collection="array" item="moduleId" open="(" separator="," close=")">
            #{moduleId}
        </foreach>
    </delete>
    
</mapper> 